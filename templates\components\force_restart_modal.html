<!-- 强制重启确认模态框 -->
<div class="modal fade" id="forceRestartModal" tabindex="-1" aria-labelledby="forceRestartModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header bg-danger text-white">
                <h5 class="modal-title" id="forceRestartModalLabel">
                    <i class="fas fa-power-off me-2"></i>强制重启设备
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 设备信息显示 -->
                <div class="mb-3">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>警告：</strong>强制重启将立即重新启动设备，这可能会中断当前的充电过程。
                    </div>
                </div>

                <div class="mb-3">
                    <label class="form-label">设备信息</label>
                    <div class="card bg-light">
                        <div class="card-body py-2">
                            <div class="row">
                                <div class="col-6">
                                    <strong>设备ID:</strong> {{ device.device_id }}
                                </div>
                                <div class="col-6">
                                    <strong>设备备注:</strong> {{ device.device_remark or '无' }}
                                </div>
                            </div>
                            <div class="row mt-1">
                                <div class="col-6">
                                    <strong>设备类型:</strong> {{ device.device_type_name }}
                                </div>
                                <div class="col-6">
                                    <strong>固件版本:</strong> {{ device.firmware_version or '未知' }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全确认输入 -->
                <div class="mb-3">
                    <label for="confirmRestartInput" class="form-label">
                        安全确认 <span class="text-danger">*</span>
                    </label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-keyboard"></i></span>
                        <input type="text" class="form-control" id="confirmRestartInput" 
                               placeholder="请输入 'RESTART' 确认重启操作">
                    </div>
                    <div class="form-text text-muted">
                        为了防止误操作，请输入 <strong>RESTART</strong>（不区分大小写）来确认重启操作
                    </div>
                </div>

                <!-- 操作状态显示 -->
                <div id="restartStatus" class="d-none">
                    <div class="alert alert-info mb-0" role="alert">
                        <div class="d-flex align-items-center">
                            <div class="spinner-border spinner-border-sm me-3" role="status" aria-hidden="true"></div>
                            <div>
                                <strong>正在执行重启操作...</strong>
                                <div class="small text-muted">请等待设备响应</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                    <i class="fas fa-times me-1"></i>取消
                </button>
                <button type="button" class="btn btn-danger" id="confirmRestartBtn" disabled>
                    <i class="fas fa-power-off me-1"></i>确认重启
                </button>
            </div>
        </div>
    </div>
</div>

<script>
// 强制重启相关功能
function showForceRestartModal() {
    // 重置模态框状态
    resetForceRestartModal();
    
    // 显示模态框
    var restartModal = new bootstrap.Modal(document.getElementById('forceRestartModal'));
    restartModal.show();
}

function resetForceRestartModal() {
    // 清空输入框
    document.getElementById('confirmRestartInput').value = '';
    
    // 禁用确认按钮
    document.getElementById('confirmRestartBtn').disabled = true;
    
    // 隐藏状态显示
    document.getElementById('restartStatus').classList.add('d-none');
    
    // 重置确认按钮文本
    const confirmBtn = document.getElementById('confirmRestartBtn');
    confirmBtn.innerHTML = '<i class="fas fa-power-off me-1"></i>确认重启';
    confirmBtn.disabled = true;
}

// 监听确认输入框变化
document.getElementById('confirmRestartInput').addEventListener('input', function() {
    const input = this.value.trim().toLowerCase();
    const confirmBtn = document.getElementById('confirmRestartBtn');
    
    if (input === 'restart') {
        confirmBtn.disabled = false;
        confirmBtn.classList.remove('btn-secondary');
        confirmBtn.classList.add('btn-danger');
    } else {
        confirmBtn.disabled = true;
        confirmBtn.classList.remove('btn-danger');
        confirmBtn.classList.add('btn-secondary');
    }
});

// 确认重启按钮事件
document.getElementById('confirmRestartBtn').addEventListener('click', async function() {
    const deviceId = {{ device.id }};
    
    // 显示执行状态
    document.getElementById('restartStatus').classList.remove('d-none');
    
    // 禁用按钮
    this.disabled = true;
    this.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>重启中...';
    
    try {
        const response = await fetch(`/api/device/${deviceId}/force_restart`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                confirm: 'RESTART'
            })
        });

        const data = await response.json();

        if (data.success) {
            // 显示成功消息
            showNotification('设备重启命令已发送', 'success');
            
            // 关闭模态框
            const modal = bootstrap.Modal.getInstance(document.getElementById('forceRestartModal'));
            modal.hide();
        } else {
            throw new Error(data.message || '重启命令发送失败');
        }
    } catch (error) {
        console.error('强制重启失败:', error);
        showNotification('重启失败: ' + error.message, 'error');
        
        // 恢复按钮状态
        this.disabled = false;
        this.innerHTML = '<i class="fas fa-power-off me-1"></i>确认重启';
        document.getElementById('restartStatus').classList.add('d-none');
    }
});

// 通知函数（如果页面没有定义的话）
if (typeof showNotification !== 'function') {
    function showNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} alert-dismissible fade show position-fixed`;
        notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
        notification.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        `;

        // 添加到页面
        document.body.appendChild(notification);

        // 3秒后自动移除
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, 3000);
    }
}
</script>