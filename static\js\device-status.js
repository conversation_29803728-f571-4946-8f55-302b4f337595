/**
 * 设备状态管理模块
 * 负责设备状态的获取、更新和显示
 */

// 设备状态查询间隔（毫秒）
var statusCheckInterval = 3000;
var statusCheckTimer = null;

/**
 * 获取设备状态
 * 从后端API获取所有设备的状态信息并更新页面显示
 */
function fetchDeviceStatus() {
    fetch('/api/device_status')
        .then(response => response.json())
        .then(data => {
            console.log('获取设备状态:', data);

            // 更新设备状态显示
            Object.entries(data).forEach(([deviceId, status]) => {
                // 更新状态标签
                const statusBadge = document.querySelector(`[data-device-status="${deviceId}"]`);
                if (statusBadge) {
                    const isOnline = status.is_online;
                    statusBadge.innerHTML = isOnline ?
                        '<i class="fas fa-circle me-1"></i>在线' :
                        '<i class="fas fa-circle me-1"></i>离线';
                    statusBadge.className = isOnline ?
                        'badge bg-success-subtle text-success' :
                        'badge bg-danger-subtle text-danger';
                }

                // 更新最后检查时间
                const lastCheckSpan = document.querySelector(`[data-device-last-check="${deviceId}"]`);
                if (lastCheckSpan) {
                    // 使用当前时间作为最后在线时间，如果设备在线
                    if (status.is_online) {
                        const now = new Date();
                        const formattedTime = now.getFullYear() + '-' +
                            String(now.getMonth() + 1).padStart(2, '0') + '-' +
                            String(now.getDate()).padStart(2, '0') + ' ' +
                            String(now.getHours()).padStart(2, '0') + ':' +
                            String(now.getMinutes()).padStart(2, '0') + ':' +
                            String(now.getSeconds()).padStart(2, '0');
                        status.last_online_time = formattedTime;
                    }
                    lastCheckSpan.innerHTML = `<i class="far fa-clock me-1"></i>${status.last_online_time || '未知'}`;
                }

                // 确保不会覆盖设备备注
                // 设备备注应该保持不变，不受WebSocket更新影响
            });

            // 更新在线设备数量统计
            const onlineCount = Object.values(data).filter(status => status.is_online).length;
            const totalCount = Object.keys(data).length;

            // 更新首页的统计卡片
            const onlineDevicesCount = document.querySelector('#onlineDevicesCount');
            if (onlineDevicesCount) {
                onlineDevicesCount.textContent = onlineCount;
            }

            const offlineDevicesCount = document.querySelector('#offlineDevicesCount');
            if (offlineDevicesCount) {
                offlineDevicesCount.textContent = totalCount - onlineCount;
            }

            // 更新设备列表中的状态显示
            const deviceRows = document.querySelectorAll('tbody tr');
            deviceRows.forEach(row => {
                const deviceId = row.querySelector('.device-checkbox')?.value;
                if (deviceId && data[deviceId]) {
                    const status = data[deviceId];
                    // 使用data-device-status属性查找状态单元格，而不是使用固定的列索引
                    const statusCell = row.querySelector(`[data-device-status="${deviceId}"]`);
                    if (statusCell) {
                        statusCell.innerHTML = status.is_online ?
                            '<i class="fas fa-circle me-1"></i>在线' :
                            '<i class="fas fa-circle me-1"></i>离线';
                        statusCell.className = status.is_online ?
                            'badge bg-success-subtle text-success' :
                            'badge bg-danger-subtle text-danger';
                    }
                }
            });
        })
        .catch(error => {
            console.error('获取设备状态失败:', error);
            // 显示错误提示
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => alert.remove());

            const alertDiv = document.createElement('div');
            alertDiv.className = 'alert alert-danger alert-dismissible fade show';
            alertDiv.innerHTML = `
                <i class="fas fa-exclamation-circle me-2"></i>获取设备状态失败，请检查网络连接
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            `;
            document.querySelector('.container').insertBefore(alertDiv, document.querySelector('.container').firstChild);
        });
}

/**
 * 设置状态查询间隔
 * @param {number} interval - 查询间隔（秒）
 */
function setStatusCheckInterval(interval) {
    console.log('设置状态查询间隔:', interval);

    // 发送请求到后端API
    fetch('/api/device_status/interval', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ interval: interval })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('状态查询间隔设置成功:', data.message);
            // 更新前端显示
            // statusCheckInterval = statusCheckInterval;

            // // 清除现有定时器
            // if (statusCheckTimer) {
            //     clearInterval(statusCheckTimer);
            // }

            // // 设置新的定时器
            // statusCheckTimer = setInterval(fetchDeviceStatus, statusCheckInterval);

            // // 立即执行一次查询
            // fetchDeviceStatus();
        } else {
            console.error('设置状态查询间隔失败:', data.message);
            alert('设置状态查询间隔失败: ' + data.message);
        }
    })
    .catch(error => {
        console.error('设置状态查询间隔出错:', error);
        alert('设置状态查询间隔出错，请检查网络连接');
    });
}

/**
 * 获取状态查询间隔
 * 从后端获取当前设置的查询间隔并更新前端显示
 */
function getStatusCheckInterval() {
    fetch('/api/device_status/interval')
        .then(response => response.json())
        .then(data => {
            console.log('获取状态查询间隔:', data);

            // 更新前端显示 - 尝试查找不同的选择器ID
            const intervalSelect = document.querySelector('#intervalSelect') || document.querySelector('#statusInterval');
            if (intervalSelect) {
                // 将秒数转换为对应的选项值
                let intervalValue = '1m'; // 默认值
                if (data.interval === 10) {
                    intervalValue = '10s';
                } else if (data.interval === 60) {
                    intervalValue = '1m';
                } else if (data.interval === 1800) {
                    intervalValue = '30m';
                } else if (data.interval === 7200) {
                    intervalValue = '2h';
                }

                intervalSelect.value = intervalValue;

            } else {
                console.log('未找到状态查询间隔选择器元素');
            }
        })
        .catch(error => {
            console.error('获取状态查询间隔失败:', error);
        });
}

/**
 * 初始化设备状态管理
 * 在页面加载完成后自动调用
 */
function initDeviceStatus() {
    fetchDeviceStatus();
    getStatusCheckInterval();
}

// 页面加载时启动设备状态管理
document.addEventListener('DOMContentLoaded', initDeviceStatus);

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        fetchDeviceStatus,
        setStatusCheckInterval,
        getStatusCheckInterval,
        initDeviceStatus
    };
}
