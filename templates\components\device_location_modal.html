<!-- 设备位置查询模态框 -->
<div class="modal fade" id="deviceLocationModal" tabindex="-1" aria-labelledby="deviceLocationModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deviceLocationModalLabel">设备位置信息</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div id="deviceLocationLoading" class="text-center">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">加载中...</span>
                    </div>
                    <p class="mt-2">正在获取设备位置信息...</p>
                </div>
                <div id="deviceLocationContent" class="d-none">
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-info text-white">
                                    <h6 class="mb-0">位置信息</h6>
                                </div>
                                <div class="card-body">
                                    <table class="table table-sm">
                                        <tbody>
                                            <tr>
                                                <th style="width: 30%;">位置代码:</th>
                                                <td id="location_code">--</td>
                                            </tr>
                                            <tr>
                                                <th>纬度:</th>
                                                <td id="location_latitude">--</td>
                                            </tr>
                                            <tr>
                                                <th>经度:</th>
                                                <td id="location_longitude">--</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header bg-success text-white">
                                    <h6 class="mb-0">地图显示</h6>
                                </div>
                                <div class="card-body">
                                    <div id="location_map" style="height: 300px; width: 100%; border: 1px solid #dee2e6; border-radius: 0.375rem;">
                                        <div id="map_loading" class="d-flex align-items-center justify-content-center h-100">
                                            <div class="text-center">
                                                <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                                                <p class="text-muted">正在加载地图...</p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-3">
                        <div class="col-12">
                            <div class="alert alert-info">
                                <h6><i class="fas fa-info-circle me-2"></i>说明</h6>
                                <ul class="mb-0">
                                    <li><strong>位置代码</strong>: 设备的位置标识码</li>
                                    <li><strong>纬度/经度</strong>: 设备的GPS坐标信息</li>
                                    <li>位置信息用于设备的地理定位和管理</li>
                                    <li>坐标采用WGS84坐标系统</li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
                <div id="deviceLocationError" class="alert alert-danger d-none">
                    获取设备位置信息失败，请重试。
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="queryDeviceLocationModal(true)">刷新</button>
            </div>
        </div>
    </div>
</div>

<!-- 高德地图API -->
<script src="https://webapi.amap.com/maps?v=1.4.15&key=6f025e700cbacbb0bb866712d20bb35c&plugin=AMap.Scale,AMap.ToolBar"></script>

<script>
// 设备位置地图管理器
class DeviceLocationMapManager {
    constructor() {
        this.map = null;
        this.marker = null;
        this.isMapInitialized = false;

        // 配置
        this.config = {
            defaultCenter: [116.397428, 39.90923],
            defaultZoom: 15,
            deviceIcon: 'https://webapi.amap.com/theme/v1.3/markers/n/mark_b.png'
        };
    }

    /**
     * 初始化地图
     */
    initMap() {
        if (this.isMapInitialized) {
            return Promise.resolve();
        }

        return new Promise((resolve, reject) => {
            try {
                // 清空地图容器
                const mapContainer = document.getElementById('location_map');
                mapContainer.innerHTML = '';

                this.map = new AMap.Map('location_map', {
                    resizeEnable: true,
                    zoom: this.config.defaultZoom,
                    center: this.config.defaultCenter,
                    mapStyle: 'amap://styles/normal'
                });

                // 添加控件
                this.map.addControl(new AMap.Scale());
                this.map.addControl(new AMap.ToolBar({
                    position: 'RB'
                }));

                // 地图加载完成
                this.map.on('complete', () => {
                    console.log('设备位置地图初始化完成');
                    this.isMapInitialized = true;
                    resolve();
                });

                // 地图加载失败
                this.map.on('error', (error) => {
                    console.error('地图加载失败:', error);
                    reject(error);
                });

            } catch (error) {
                console.error('地图初始化失败:', error);
                reject(error);
            }
        });
    }

    /**
     * 显示设备位置
     */
    showDeviceLocation(latitude, longitude, deviceName = '设备位置') {
        if (!this.map) {
            console.error('地图未初始化');
            return;
        }

        // 清除现有标记
        if (this.marker) {
            this.marker.setMap(null);
            this.marker = null;
        }

        // 创建设备标记
        this.marker = new AMap.Marker({
            position: [longitude, latitude],
            title: deviceName,
            icon: new AMap.Icon({
                size: new AMap.Size(32, 32),
                image: this.config.deviceIcon,
                imageSize: new AMap.Size(32, 32)
            })
        });

        this.marker.setMap(this.map);

        // 创建信息窗口
        const content = `
            <div style="padding: 10px; min-width: 200px;">
                <h6 style="margin: 0 0 10px 0; color: #333;">
                    ${deviceName}
                </h6>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-map-marker-alt"></i>
                    纬度: ${latitude.toFixed(7)}°
                </p>
                <p style="margin: 5px 0; font-size: 12px;">
                    <i class="fas fa-map-marker-alt"></i>
                    经度: ${longitude.toFixed(7)}°
                </p>
            </div>
        `;

        const infoWindow = new AMap.InfoWindow({
            content: content,
            offset: new AMap.Pixel(0, -30)
        });

        this.marker.on('click', () => {
            infoWindow.open(this.map, this.marker.getPosition());
        });

        // 设置地图中心和缩放级别
        this.map.setCenter([longitude, latitude]);
        this.map.setZoom(16);

        console.log(`设备位置已显示: ${latitude}, ${longitude}`);
    }

    /**
     * 清除地图
     */
    clearMap() {
        if (this.marker) {
            this.marker.setMap(null);
            this.marker = null;
        }

        if (this.map) {
            this.map.destroy();
            this.map = null;
            this.isMapInitialized = false;
        }
    }
}

// 全局地图管理器实例
let deviceLocationMapManager = null;

// 查询设备位置信息（模态框版本）
function queryDeviceLocationModal(isRefresh = false) {
    // 显示模态框
    if (!isRefresh) {
        const modal = new bootstrap.Modal(document.getElementById('deviceLocationModal'));
        modal.show();

        // 模态框显示后初始化地图管理器
        document.getElementById('deviceLocationModal').addEventListener('shown.bs.modal', function() {
            if (!deviceLocationMapManager) {
                deviceLocationMapManager = new DeviceLocationMapManager();
            }
        }, { once: true });

        // 模态框隐藏时清理地图
        document.getElementById('deviceLocationModal').addEventListener('hidden.bs.modal', function() {
            if (deviceLocationMapManager) {
                deviceLocationMapManager.clearMap();
                deviceLocationMapManager = null;
            }
        });
    }

    // 显示加载中状态
    document.getElementById('deviceLocationLoading').classList.remove('d-none');
    document.getElementById('deviceLocationContent').classList.add('d-none');
    document.getElementById('deviceLocationError').classList.add('d-none');

    // 发送请求获取设备位置信息
    fetch(`/api/device/${window.deviceId}/location`)
        .then(response => {
            if (!response.ok) {
                throw new Error('网络请求失败');
            }
            return response.json();
        })
        .then(data => {
            // 隐藏加载中状态
            document.getElementById('deviceLocationLoading').classList.add('d-none');

            if (data.error) {
                // 显示错误信息
                document.getElementById('deviceLocationError').textContent = '获取设备位置信息失败: ' + data.error;
                document.getElementById('deviceLocationError').classList.remove('d-none');
                return;
            }

            if (data.success && data.location) {
                // 解析并显示位置信息
                displayDeviceLocation(data.location);
                // 显示内容区域
                document.getElementById('deviceLocationContent').classList.remove('d-none');
            } else {
                // 显示成功但无具体位置信息的情况
                document.getElementById('deviceLocationError').textContent = '设备位置查询成功，但未返回具体位置信息';
                document.getElementById('deviceLocationError').classList.remove('d-none');
            }
        })
        .catch(error => {
            console.error('获取设备位置信息失败:', error);
            // 隐藏加载中状态，显示错误信息
            document.getElementById('deviceLocationLoading').classList.add('d-none');
            document.getElementById('deviceLocationError').textContent = '获取设备位置信息失败: ' + error.message;
            document.getElementById('deviceLocationError').classList.remove('d-none');
        });
}

// 显示设备位置信息
function displayDeviceLocation(data) {
    // 检查是否有位置信息
    if (!data) {
        document.getElementById('deviceLocationError').textContent = '设备返回的位置信息格式不正确';
        document.getElementById('deviceLocationError').classList.remove('d-none');
        return;
    }

    // 更新位置信息
    document.getElementById('location_code').textContent = data.location_code || '--';
    document.getElementById('location_latitude').textContent = data.latitude ? data.latitude.toFixed(7) + '°' : '--';
    document.getElementById('location_longitude').textContent = data.longitude ? data.longitude.toFixed(7) + '°' : '--';

    // 如果有有效的坐标，初始化并显示地图
    if (data.latitude && data.longitude && data.latitude !== 0 && data.longitude !== 0) {
        // 确保地图管理器已创建
        if (!deviceLocationMapManager) {
            deviceLocationMapManager = new DeviceLocationMapManager();
        }

        // 初始化地图并显示设备位置
        deviceLocationMapManager.initMap()
            .then(() => {
                // 获取设备名称（如果有的话）
                const deviceName = window.deviceName || window.deviceId || '设备位置';
                deviceLocationMapManager.showDeviceLocation(data.latitude, data.longitude, deviceName);
            })
            .catch(error => {
                console.error('地图初始化失败:', error);
                // 显示地图加载失败的提示
                const mapContainer = document.getElementById('location_map');
                mapContainer.innerHTML = `
                    <div class="d-flex align-items-center justify-content-center h-100">
                        <div class="text-center">
                            <i class="fas fa-exclamation-triangle fa-3x text-warning mb-3"></i>
                            <p class="text-warning mb-1">地图加载失败</p>
                            <p class="text-success mb-1">位置: ${data.latitude.toFixed(7)}°, ${data.longitude.toFixed(7)}°</p>
                            <small class="text-muted">请检查网络连接或刷新重试</small>
                        </div>
                    </div>
                `;
            });
    } else {
        // 没有有效坐标时显示提示
        const mapContainer = document.getElementById('location_map');
        mapContainer.innerHTML = `
            <div class="d-flex align-items-center justify-content-center h-100">
                <div class="text-center">
                    <i class="fas fa-map-marker-alt fa-3x text-muted mb-3"></i>
                    <p class="text-muted">暂无有效的位置坐标</p>
                    <small class="text-muted">设备可能尚未上报位置信息</small>
                </div>
            </div>
        `;
    }
}
</script>
