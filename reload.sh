#!/bin/bash
# OTA 设备管理系统重启脚本
# 自动杀死现有进程并重新启动应用

echo "正在重启 OTA 设备管理系统..."
echo "======================================"

# 定义日志文件路径
LOG_FILE="output.log"

# 切换到脚本所在目录
cd "$(dirname "$0")"

# 查找并杀死现有的 Python 应用进程
echo "正在查找现有进程..."
PIDS=$(ps aux | grep "python.*app.py" | grep -v grep | awk '{print $2}')

if [ -n "$PIDS" ]; then
    echo "发现现有进程，正在停止..."
    for PID in $PIDS; do
        echo "停止进程 $PID"
        kill -TERM $PID
        sleep 2
        # 如果进程仍然存在，强制杀死
        if kill -0 $PID 2>/dev/null; then
            echo "强制停止进程 $PID"
            kill -KILL $PID
        fi
    done
    echo "所有现有进程已停止"
else
    echo "未发现现有进程"
fi

# 等待一下确保端口释放
echo "等待端口释放..."
sleep 3

# 检查端口5000是否被占用
if lsof -Pi :5000 -sTCP:LISTEN -t >/dev/null 2>&1; then
    echo "警告: 端口5000仍被占用，尝试释放..."
    PORT_PID=$(lsof -Pi :5000 -sTCP:LISTEN -t)
    if [ -n "$PORT_PID" ]; then
        kill -TERM $PORT_PID
        sleep 2
        if kill -0 $PORT_PID 2>/dev/null; then
            kill -KILL $PORT_PID
        fi
    fi
fi

echo "正在重新启动应用..."

# 设置环境变量
export FLASK_ENV=production
export DATABASE_URL="postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/KafangCharging"

# 启动应用（生产环境配置：host=0.0.0.0, port=5000, debug=False）
nohup python3 -OO app.py --host 0.0.0.0 --port 5000 --no-debug > "$LOG_FILE" 2>&1 &

# 获取新进程ID
NEW_PID=$!
echo "服务已重新启动，新进程ID为 $NEW_PID"
echo "日志输出在 $LOG_FILE"
echo "服务地址: http://0.0.0.0:5000"
echo "调试模式: 关闭"

# 等待一下并检查进程是否正常启动
sleep 3
if kill -0 $NEW_PID 2>/dev/null; then
    echo "✓ 服务启动成功"
else
    echo "✗ 服务启动失败，请检查日志文件 $LOG_FILE"
    exit 1
fi

echo "重启完成！"
