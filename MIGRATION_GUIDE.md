# 生产环境数据库迁移指南

## 概述

本指南说明如何将开发环境的数据库优化迁移到生产环境。迁移将把原有的4个分离表合并为2个优化表，并建立完整的外键关系。

## 迁移内容

### 数据库结构变化
- **原结构**: 4个分离表 (`device`, `device_locations`, `device_parameter`, `debug_script`)
- **新结构**: 2个优化表 (`device`, `register_config`)

### 主要改进
- ✅ 数据结构简化，提高查询性能
- ✅ 寄存器配置与数据分离，便于管理
- ✅ 支持动态配置，无需硬编码
- ✅ 完整的外键约束，确保数据完整性
- ✅ PostgreSQL ARRAY类型，高效存储寄存器值

## 迁移脚本

### 1. 最终迁移脚本 (推荐)
```bash
# 查看迁移步骤 (不执行)
python sql_tools/production_migration_final.py --dry-run

# 测试环境迁移
python sql_tools/production_migration_final.py

# 生产环境迁移 (需要确认)
python sql_tools/production_migration_final.py --production

# 回滚迁移 (如果需要)
python sql_tools/production_migration_final.py --rollback TIMESTAMP
```

### 2. Python版本
```bash
python sql_tools/production_migration.py
```

### 3. SQL版本
```sql
psql -h host -U user -d database -f sql_tools/production_migration.sql
```

## 迁移步骤详解

### 步骤1: 备份当前状态
- 创建 `device_new_backup_TIMESTAMP` 表
- 创建 `register_config_backup_TIMESTAMP` 表
- 确保数据安全

### 步骤2: 删除旧表
- 删除原有的 `device` 表（如果存在）
- 清理旧的约束和索引

### 步骤3: 重命名表和序列
- `device_new` → `device`
- `device_new_id_seq` → `device_id_seq`
- 更新序列所有者

### 步骤4: 修复数据类型
- 将 `batch_ota_detail.device_id` 从 `integer` 改为 `varchar`
- 将 `ota_task.device_id` 从 `integer` 改为 `varchar`
- 确保类型一致性

### 步骤5: 清理孤立数据
- 删除引用不存在设备的记录
- 确保外键约束能够成功创建

### 步骤6: 创建外键约束
- 为所有相关表创建外键约束
- 包括时间序列数据表和月份分区表
- 设置 `ON DELETE CASCADE`

### 步骤7: 验证迁移结果
- 检查表记录数量
- 验证外键约束数量
- 确认数据完整性

## 生产环境配置

在运行生产环境迁移前，请修改 `sql_tools/production_migration_final.py` 中的配置：

```python
PRODUCTION_CONFIG = {
    'host': 'your_production_host',
    'port': 5432,
    'database': 'your_production_database',
    'user': 'your_production_user',
    'password': 'your_production_password',
    'schema': 'your_production_schema'
}
```

## 安全措施

### 1. 自动备份
- 每次迁移前自动创建备份表
- 备份表包含时间戳，便于识别

### 2. 事务保护
- 使用数据库事务确保原子性
- 失败时自动回滚所有更改

### 3. 回滚机制
```bash
# 如果迁移后发现问题，可以回滚
python sql_tools/production_migration_final.py --rollback 20250919_235527
```

### 4. 验证检查
- 迁移后自动验证数据完整性
- 检查记录数量和约束数量
- 确保所有功能正常

## 预期结果

### 迁移前
```
表结构: device, device_locations, device_parameter, debug_script (4个表)
外键约束: 0个
数据分散: 多表存储，查询复杂
```

### 迁移后
```
表结构: device, register_config (2个表)
外键约束: 15+ 个
数据集中: 统一存储，查询高效
寄存器配置: 支持动态管理
```

## 故障排除

### 常见问题

1. **外键创建失败**
   - 原因: 存在孤立数据
   - 解决: 脚本会自动清理孤立数据

2. **数据类型不匹配**
   - 原因: device_id类型不一致
   - 解决: 脚本会自动转换数据类型

3. **权限不足**
   - 原因: 数据库用户权限不够
   - 解决: 确保用户有DDL权限

### 回滚步骤

如果迁移失败或需要回滚：

1. 使用回滚命令
2. 检查备份表是否存在
3. 验证数据完整性
4. 重新启动应用服务

## 测试建议

1. **在测试环境先运行**
2. **验证所有功能正常**
3. **检查性能是否提升**
4. **确认前端页面正常显示**
5. **测试寄存器配置管理**

## 联系支持

如果在迁移过程中遇到问题，请：
1. 保存完整的错误日志
2. 记录迁移的时间戳
3. 不要手动修改数据库
4. 联系技术支持团队

---

**⚠️ 重要提醒**: 
- 生产环境迁移前请务必做好完整备份
- 建议在业务低峰期执行迁移
- 迁移后请充分测试所有功能
