#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
测试迁移后的时序数据API功能
验证新的数据库结构是否正常工作
"""

import os
import sys
import logging
import requests
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class TimeSeriesAPITester:
    """时序数据API测试类"""
    
    def __init__(self, base_url: str = "http://localhost:5000"):
        self.base_url = base_url
        self.session = requests.Session()
        self.test_device_id = "test_device_migration_001"
        
    def test_write_sensor_data(self) -> bool:
        """测试写入传感器数据"""
        try:
            logger.info("测试写入传感器数据...")
            
            # 准备测试数据
            test_data = {
                'device_id': self.test_device_id,
                'bl0910_error_count': 0,
                'bl0910_rms_values': [1500, 1520, 1480, 1510, 1490, 1505, 1515, 1485, 1495, 1500],
                'relay_state': 0b1111111111000000,  # 前10个继电器开启
                'short_period_error_count': 0,
                'long_period_error_count': 0,
                'last_zero_cross_time': int(datetime.now().timestamp()),
                'voltage': 220.5,
                'temperature': 35.8,
                'total_power': 1500.25,
                'csq': 25,
                'ber': 2,
                'relay_pull_fault': 0,
                'relay_open_fault': 0
            }
            
            # 发送写入请求
            response = self.session.post(
                f"{self.base_url}/api/time_series/write",
                json=test_data,
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 写入传感器数据成功: {result}")
                return True
            else:
                logger.error(f"❌ 写入传感器数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 写入传感器数据异常: {e}")
            return False
    
    def test_write_hardware_data(self) -> bool:
        """测试写入硬件数据包"""
        try:
            logger.info("测试写入硬件数据包...")
            
            # 创建模拟硬件数据包
            hardware_data = self._create_mock_hardware_data()
            
            # 发送写入请求
            response = self.session.post(
                f"{self.base_url}/api/time_series/write_hardware",
                json={
                    'device_id': self.test_device_id,
                    'hardware_data': hardware_data.hex()  # 转换为十六进制字符串
                },
                headers={'Content-Type': 'application/json'}
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 写入硬件数据包成功: {result}")
                return True
            else:
                logger.error(f"❌ 写入硬件数据包失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 写入硬件数据包异常: {e}")
            return False
    
    def test_query_voltage_data(self) -> bool:
        """测试查询电压数据"""
        try:
            logger.info("测试查询电压数据...")
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            response = self.session.get(
                f"{self.base_url}/api/time_series/voltage",
                params={
                    'device_id': self.test_device_id,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 查询电压数据成功: 找到 {len(result.get('data', []))} 条记录")
                
                # 验证数据格式
                if result.get('data'):
                    sample = result['data'][0]
                    required_fields = ['timestamp', 'voltage']
                    if all(field in sample for field in required_fields):
                        logger.info(f"✅ 电压数据格式正确: {sample}")
                        return True
                    else:
                        logger.error(f"❌ 电压数据格式错误: {sample}")
                        return False
                else:
                    logger.info("✅ 查询成功但没有数据（正常情况）")
                    return True
            else:
                logger.error(f"❌ 查询电压数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 查询电压数据异常: {e}")
            return False
    
    def test_query_temperature_data(self) -> bool:
        """测试查询温度数据"""
        try:
            logger.info("测试查询温度数据...")
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            response = self.session.get(
                f"{self.base_url}/api/time_series/temperature",
                params={
                    'device_id': self.test_device_id,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 查询温度数据成功: 找到 {len(result.get('data', []))} 条记录")
                return True
            else:
                logger.error(f"❌ 查询温度数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 查询温度数据异常: {e}")
            return False
    
    def test_query_power_data(self) -> bool:
        """测试查询功率数据"""
        try:
            logger.info("测试查询功率数据...")
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            response = self.session.get(
                f"{self.base_url}/api/time_series/power",
                params={
                    'device_id': self.test_device_id,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 查询功率数据成功: 找到 {len(result.get('data', []))} 条记录")
                return True
            else:
                logger.error(f"❌ 查询功率数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 查询功率数据异常: {e}")
            return False
    
    def test_query_relay_data(self) -> bool:
        """测试查询继电器数据"""
        try:
            logger.info("测试查询继电器数据...")
            
            end_time = datetime.now()
            start_time = end_time - timedelta(hours=1)
            
            response = self.session.get(
                f"{self.base_url}/api/time_series/relay",
                params={
                    'device_id': self.test_device_id,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat()
                }
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 查询继电器数据成功: 找到 {len(result.get('data', []))} 条记录")
                return True
            else:
                logger.error(f"❌ 查询继电器数据失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 查询继电器数据异常: {e}")
            return False
    
    def test_data_statistics(self) -> bool:
        """测试数据统计"""
        try:
            logger.info("测试数据统计...")
            
            response = self.session.get(
                f"{self.base_url}/api/time_series/stats",
                params={'device_id': self.test_device_id}
            )
            
            if response.status_code == 200:
                result = response.json()
                logger.info(f"✅ 数据统计成功: {result}")
                return True
            else:
                logger.error(f"❌ 数据统计失败: {response.status_code} - {response.text}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 数据统计异常: {e}")
            return False
    
    def _create_mock_hardware_data(self) -> bytearray:
        """创建模拟硬件数据包"""
        hardware_data = bytearray(80)
        pos = 0
        
        # 会话ID (2字节)
        hardware_data[pos:pos+2] = (0x1234).to_bytes(2, byteorder='little')
        pos += 2
        
        # 响应结果 (1字节) - 成功
        hardware_data[pos] = 0
        pos += 1
        
        # BL0910错误计数 (4字节)
        hardware_data[pos:pos+4] = (5).to_bytes(4, byteorder='little')
        pos += 4
        
        # 10个通道的BL0910 RMS寄存器值 (每个4字节)
        for i in range(10):
            value = 1600 + i * 20
            hardware_data[pos:pos+4] = value.to_bytes(4, byteorder='little')
            pos += 4
        
        # 继电器状态 (2字节)
        hardware_data[pos:pos+2] = (0b1111111111000000).to_bytes(2, byteorder='little')
        pos += 2
        
        # 短周期错误计数 (2字节)
        hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
        pos += 2
        
        # 长周期错误计数 (2字节)
        hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
        pos += 2
        
        # 最后零交叉时间 (4字节)
        hardware_data[pos:pos+4] = int(datetime.now().timestamp()).to_bytes(4, byteorder='little')
        pos += 4
        
        # 电压 (2字节) - 220.80V
        hardware_data[pos:pos+2] = (22080).to_bytes(2, byteorder='little')
        pos += 2
        
        # 温度 (2字节) - 36.20°C
        hardware_data[pos:pos+2] = (3620).to_bytes(2, byteorder='little')
        pos += 2
        
        # 总功率 (4字节) - 1520.50W
        hardware_data[pos:pos+4] = (152050).to_bytes(4, byteorder='little')
        pos += 4
        
        # CSQ (1字节)
        hardware_data[pos] = 28
        pos += 1
        
        # BER (1字节)
        hardware_data[pos] = 1
        pos += 1
        
        # 继电器闭合故障 (2字节)
        hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
        pos += 2
        
        # 继电器断开故障 (2字节)
        hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
        pos += 2
        
        return hardware_data
    
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        logger.info("开始API功能测试")
        print("=" * 60)
        print("时序数据API功能测试")
        print("=" * 60)
        
        tests = [
            ("写入传感器数据", self.test_write_sensor_data),
            ("写入硬件数据包", self.test_write_hardware_data),
            ("查询电压数据", self.test_query_voltage_data),
            ("查询温度数据", self.test_query_temperature_data),
            ("查询功率数据", self.test_query_power_data),
            ("查询继电器数据", self.test_query_relay_data),
            ("数据统计", self.test_data_statistics),
        ]
        
        passed = 0
        failed = 0
        
        for test_name, test_func in tests:
            print(f"\n测试: {test_name}")
            print("-" * 40)
            
            try:
                if test_func():
                    print(f"✅ {test_name} - 通过")
                    passed += 1
                else:
                    print(f"❌ {test_name} - 失败")
                    failed += 1
            except Exception as e:
                print(f"❌ {test_name} - 异常: {e}")
                failed += 1
        
        print("\n" + "=" * 60)
        print(f"测试结果: 通过 {passed} 个, 失败 {failed} 个")
        print("=" * 60)
        
        if failed == 0:
            print("🎉 所有测试通过！新的时序数据API工作正常")
            return True
        else:
            print(f"⚠️  有 {failed} 个测试失败，请检查相关功能")
            return False


def main():
    """主函数"""
    print("时序数据API测试工具")
    print("=" * 50)
    print("此工具将测试迁移后的时序数据API功能")
    print()
    
    # 获取服务器地址
    base_url = input("请输入服务器地址 (默认: http://localhost:5000): ").strip()
    if not base_url:
        base_url = "http://localhost:5000"
    
    # 创建测试器
    tester = TimeSeriesAPITester(base_url)
    
    # 运行测试
    try:
        success = tester.run_all_tests()
        
        if success:
            print("\n✅ 所有API测试通过，迁移成功！")
        else:
            print("\n❌ 部分API测试失败，请检查相关功能")
    
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")


if __name__ == "__main__":
    main()
