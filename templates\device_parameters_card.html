<!-- 设备参数悬停卡片模板（隐藏） -->
<template id="deviceParametersCardTemplate">
    <div class="device-parameters-card">
        <div class="card-header">
            <div class="card-title">
                <i class="fas fa-microchip"></i>
                <span class="card-device-id">设备参数</span>
            </div>
        </div>
        <div class="card-body">
            <!-- 参数筛选区域 -->
            <div class="parameters-filter">
                <div class="d-flex align-items-center gap-2">
                    <label class="form-label mb-0 small">显示参数:</label>
                    <select class="form-select form-select-sm parameters-filter-select">
                        <option value="all">全部参数</option>
                        <option value="version">版本信息</option>
                        <option value="temperature">温度参数</option>
                        <option value="power">功率参数</option>
                        <option value="control">控制参数</option>
                        <option value="error">错误计数</option>
                        <option value="location">位置信息</option>
                        <option value="wireless">无线充电</option>
                    </select>
                </div>
            </div>

            <!-- 加载状态 -->
            <div class="parameters-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在获取设备参数...</div>
            </div>

            <!-- 错误状态 -->
            <div class="parameters-error" style="display: none;">
                <i class="fas fa-exclamation-triangle fa-2x mb-2"></i>
                <div>获取参数失败</div>
                <div class="small mt-1 error-message"></div>
            </div>

            <!-- 参数列表 -->
            <div class="parameters-list" style="display: none;">
                <!-- 参数项将通过JavaScript动态生成 -->
            </div>
        </div>
    </div>
</template>

<script>
    // 设备参数卡片相关的JavaScript代码
    let deviceParametersCard = null;
    let currentDeviceId = null;
    let parametersData = {};
    let hideCardTimeout = null;

    // 参数分类映射
    const parameterCategories = {
        'REG_VERSION_H': 'version',
        'REG_VERSION_L': 'version',
        'REG_COMPILER_TS_H': 'version',
        'REG_COMPILER_TS_L': 'version',
        'REG_T1': 'temperature',
        'REG_T2': 'temperature',
        'REG_T3': 'temperature',
        'REG_T4': 'temperature',
        'REG_T5': 'temperature',
        'REG_T6': 'temperature',
        'REG_T7': 'temperature',
        'REG_T8': 'temperature',
        'REG_T9': 'temperature',
        'REG_T10': 'temperature',
        'REG_T11': 'temperature',
        'REG_T12': 'temperature',
        'REG_T13': 'temperature',
        'REG_P4': 'power',
        'REG_P5': 'power',
        'REG_P6': 'power',
        'REG_P7': 'power',
        'REG_P8': 'power',
        'REG_P9': 'power',
        'REG_P10': 'power',
        'REG_P11': 'power',
        'REG_P12': 'power',
        'REG_P13': 'power',
        'REG_P14': 'power',
        'REG_P15': 'power',
        'REG_P16': 'power',
        'REG_CTRL1': 'control',
        'REG_PERSENTAGE': 'control',
        'REG_CSQ': 'control',
        'REG_ERROR_CNT1': 'error',
        'REG_ERROR_CNT2': 'error',
        'REG_ERROR_CNT3': 'error',
        'REG_ERROR_CNT4': 'error',
        'REG_LOCATION_CODE': 'location',
        'REG_LOCATION_LATITUDE_H': 'location',
        'REG_LOCATION_LATITUDE_L': 'location',
        'REG_LOCATION_LONGITUDE_H': 'location',
        'REG_LOCATION_LONGITUDE_L': 'location',
        'REG_UID_PROTECT_KEY2': 'wireless',
        'REG_WTC_MAP': 'wireless',
        'REG_WTC_CTRL': 'wireless'
    };

    // 显示设备参数卡片
    function showDeviceParametersCard(element) {
        // 清除隐藏定时器
        if (hideCardTimeout) {
            clearTimeout(hideCardTimeout);
            hideCardTimeout = null;
        }

        const deviceId = element.getAttribute('data-device-id');
        const databaseId = element.getAttribute('data-database-id');

        // 如果已经显示相同设备的卡片，直接返回
        if (deviceParametersCard && currentDeviceId === deviceId) {
            return;
        }

        // 创建卡片元素（如果不存在）
        if (!deviceParametersCard) {
            const template = document.getElementById('deviceParametersCardTemplate');
            if (!template) {
                console.error('设备参数卡片模板未找到');
                return;
            }

            deviceParametersCard = template.content.cloneNode(true).firstElementChild;
            document.body.appendChild(deviceParametersCard);

            // 添加筛选事件
            const filterSelect = deviceParametersCard.querySelector('.parameters-filter-select');
            filterSelect.addEventListener('change', filterParameters);

            // 添加鼠标事件防止卡片消失
            deviceParametersCard.addEventListener('mouseenter', function () {
                if (hideCardTimeout) {
                    clearTimeout(hideCardTimeout);
                    hideCardTimeout = null;
                }
            });

            deviceParametersCard.addEventListener('mouseleave', function () {
                hideDeviceParametersCard();
            });
        }

        currentDeviceId = deviceId;

        // 更新卡片标题
        const cardTitle = deviceParametersCard.querySelector('.card-device-id');
        cardTitle.textContent = `设备 ${deviceId} 参数`;

        // 定位卡片
        positionCard(element);

        // 显示卡片
        deviceParametersCard.classList.add('show');

        // 加载参数数据
        loadDeviceParameters(databaseId);
    }

    // 隐藏设备参数卡片
    function hideDeviceParametersCard() {
        hideCardTimeout = setTimeout(() => {
            if (deviceParametersCard) {
                deviceParametersCard.classList.remove('show');
            }
            currentDeviceId = null;
        }, 300); // 300ms延迟，给用户时间移动到卡片上
    }

    // 定位卡片
    function positionCard(element) {
        const rect = element.getBoundingClientRect();
        const cardWidth = 400;
        const cardHeight = 600;

        let left = rect.right + 10;
        let top = rect.top;

        // 检查右侧空间是否足够
        if (left + cardWidth > window.innerWidth) {
            left = rect.left - cardWidth - 10;
        }

        // 检查顶部空间是否足够
        if (top + cardHeight > window.innerHeight) {
            top = window.innerHeight - cardHeight - 10;
        }

        // 确保不会超出屏幕左侧
        if (left < 10) {
            left = 10;
        }

        // 确保不会超出屏幕顶部
        if (top < 10) {
            top = 10;
        }

        deviceParametersCard.style.left = left + 'px';
        deviceParametersCard.style.top = top + 'px';
    }

    // 加载设备参数
    function loadDeviceParameters(databaseId) {
        const loadingElement = deviceParametersCard.querySelector('.parameters-loading');
        const errorElement = deviceParametersCard.querySelector('.parameters-error');
        const listElement = deviceParametersCard.querySelector('.parameters-list');

        // 显示加载状态
        loadingElement.style.display = 'block';
        errorElement.style.display = 'none';
        listElement.style.display = 'none';

        // 调用API获取参数
        fetch(`/api/device/${databaseId}/saved_parameters`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.error) {
                    throw new Error(data.error);
                }

                parametersData = data;
                renderParameters();

                // 显示参数列表
                loadingElement.style.display = 'none';
                listElement.style.display = 'block';
            })
            .catch(error => {
                console.error('获取设备参数失败:', error);

                // 显示错误状态
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';

                const errorMessage = deviceParametersCard.querySelector('.error-message');
                errorMessage.textContent = error.message || '未知错误';
            });
    }

    // 渲染参数列表
    function renderParameters() {
        const listElement = deviceParametersCard.querySelector('.parameters-list');
        const filterValue = deviceParametersCard.querySelector('.parameters-filter-select').value;

        let html = '';

        for (const [regName, paramData] of Object.entries(parametersData)) {
            // 应用筛选
            if (filterValue !== 'all') {
                const category = parameterCategories[regName] || 'other';
                if (category !== filterValue) {
                    continue;
                }
            }

            html += `
            <div class="parameter-item">
                <div class="parameter-name">${regName}</div>
                <div class="parameter-value">${paramData.value || '--'}</div>
                <div class="parameter-description">${paramData.description || '无描述'}</div>
            </div>
        `;
        }

        if (html === '') {
            html = '<div class="parameter-item"><div class="text-muted text-center">没有匹配的参数</div></div>';
        }

        listElement.innerHTML = html;
    }

    // 筛选参数
    function filterParameters() {
        if (parametersData && Object.keys(parametersData).length > 0) {
            renderParameters();
        }
    }
</script>