@echo off
REM OTA 设备管理系统重启脚本
REM 自动杀死现有进程并重新启动应用

echo 正在重启 OTA 设备管理系统...
echo ======================================

REM 定义日志文件路径
set LOG_FILE=output.log

REM 切换到脚本所在目录
cd /d "%~dp0"

REM 查找并杀死现有的 Python 应用进程
echo 正在查找现有进程...
for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python.exe" /fo csv ^| findstr "app.py"') do (
    echo 发现进程 %%i，正在停止...
    taskkill /pid %%i /f >nul 2>&1
)

for /f "tokens=2" %%i in ('tasklist /fi "imagename eq python3.exe" /fo csv ^| findstr "app.py"') do (
    echo 发现进程 %%i，正在停止...
    taskkill /pid %%i /f >nul 2>&1
)

REM 更彻底的方式：杀死所有包含 app.py 的 python 进程
wmic process where "name='python.exe' and commandline like '%%app.py%%'" delete >nul 2>&1
wmic process where "name='python3.exe' and commandline like '%%app.py%%'" delete >nul 2>&1

echo 现有进程已停止

REM 等待一下确保端口释放
echo 等待端口释放...
timeout /t 3 /nobreak >nul

REM 检查并释放端口5000
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :5000') do (
    echo 释放端口5000上的进程 %%a
    taskkill /pid %%a /f >nul 2>&1
)

echo 正在重新启动应用...

REM 设置环境变量
set FLASK_ENV=production
set DATABASE_URL=postgresql://kafanglinlin:7jbWNHYZZLMa@localhost:5432/KafangCharging

REM 启动应用（生产环境配置）
start /b python -OO app.py --host 0.0.0.0 --port 5000 --no-debug > %LOG_FILE% 2>&1

echo 服务已重新启动
echo 日志输出在 %LOG_FILE%
echo 服务地址: http://0.0.0.0:5000
echo 调试模式: 关闭

REM 等待一下并检查服务是否启动
timeout /t 3 /nobreak >nul

REM 检查端口是否被监听
netstat -ano | findstr :5000 | findstr LISTENING >nul
if %errorlevel% equ 0 (
    echo ✓ 服务启动成功
) else (
    echo ✗ 服务启动失败，请检查日志文件 %LOG_FILE%
    pause
    exit /b 1
)

echo 重启完成！
pause
