#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化时序数据模型使用示例
展示如何使用新的原始硬件数据存储和解析功能
"""

import os
import sys
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from models.database import db
from models.time_series_data import TimeSeriesData
from services.time_series_service import TimeSeriesService
from app import create_app


def demonstrate_basic_usage():
    """演示基本使用方法"""
    print("=" * 60)
    print("简化时序数据模型使用示例")
    print("=" * 60)
    
    # 设置为开发环境
    os.environ['FLASK_ENV'] = 'development'
    
    # 创建Flask应用
    app = create_app()
    
    with app.app_context():
        # 创建数据库表
        db.create_all()
        
        # 创建服务实例
        time_series_service = TimeSeriesService()
        
        print("\n1. 写入传感器数据示例")
        print("-" * 40)
        
        device_id = "charging_pile_001"
        
        # 示例1: 使用解析后的数据写入
        sensor_data = {
            'bl0910_error_count': 0,
            'bl0910_rms_values': [
                1500, 1520, 1480, 1510, 1490,  # 通道1-5的RMS值
                1505, 1515, 1485, 1495, 1500   # 通道6-10的RMS值
            ],
            'relay_state': 0b1111111111000000,  # 前10个继电器开启，后6个关闭
            'short_period_error_count': 0,
            'long_period_error_count': 0,
            'last_zero_cross_time': int(datetime.now().timestamp()),
            'voltage': 220.5,  # 电压220.5V
            'temperature': 35.8,  # 温度35.8°C
            'total_power': 1500.25,  # 总功率1500.25W
            'csq': 25,  # 信号质量
            'ber': 2,   # 误码率
            'relay_pull_fault': 0,  # 无闭合故障
            'relay_open_fault': 0   # 无分断故障
        }
        
        success = time_series_service.write_sensor_data(device_id, **sensor_data)
        print(f"写入传感器数据: {'成功' if success else '失败'}")
        
        print("\n2. 模拟硬件数据包写入示例")
        print("-" * 40)
        
        # 示例2: 模拟从硬件信息查询得到的原始数据包
        hardware_data = create_mock_hardware_data()
        success = time_series_service.write_hardware_info_data(device_id, hardware_data)
        print(f"写入硬件数据包: {'成功' if success else '失败'}")
        
        print("\n3. 数据查询和解析示例")
        print("-" * 40)
        
        # 查询最近的数据
        end_time = datetime.now()
        start_time = end_time - timedelta(hours=1)
        
        # 查询电压数据
        voltage_data = time_series_service.query_voltage_data(device_id, start_time, end_time)
        print(f"查询到 {len(voltage_data)} 条电压数据")
        if voltage_data:
            print(f"最新电压: {voltage_data[-1]['value']:.2f}V")
        
        # 查询温度数据
        temperature_data = time_series_service.query_temperature_data(device_id, start_time, end_time)
        print(f"查询到 {len(temperature_data)} 条温度数据")
        if temperature_data:
            print(f"最新温度: {temperature_data[-1]['value']:.2f}°C")
        
        # 查询功率数据
        power_data = time_series_service.query_total_power_data(device_id, start_time, end_time)
        print(f"查询到 {len(power_data)} 条功率数据")
        if power_data:
            print(f"最新功率: {power_data[-1]['value']:.2f}W")
        
        # 查询信号质量数据
        csq_data = time_series_service.query_csq_data(device_id, start_time, end_time)
        print(f"查询到 {len(csq_data)} 条信号质量数据")
        if csq_data:
            print(f"最新信号质量: CSQ={csq_data[-1]['value']}, BER={csq_data[-1]['ber']}")
        
        print("\n4. 原始数据解析示例")
        print("-" * 40)
        
        # 直接查询数据库记录进行解析
        records = db.session.query(TimeSeriesData).filter_by(device_id=device_id).all()
        
        if records:
            latest_record = records[-1]
            print(f"设备ID: {latest_record.device_id}")
            print(f"时间戳: {latest_record.timestamp}")
            print(f"原始电压值: {latest_record.voltage_raw} (解析后: {latest_record.get_voltage():.2f}V)")
            print(f"原始温度值: {latest_record.temperature_raw} (解析后: {latest_record.get_temperature():.2f}°C)")
            print(f"原始功率值: {latest_record.total_power_raw} (解析后: {latest_record.get_total_power():.2f}W)")
            
            # 继电器状态解析
            relay_states = latest_record.get_relay_states()
            active_relays = [k for k, v in relay_states.items() if v]
            print(f"激活的继电器: {', '.join(active_relays[:5])}...")  # 只显示前5个
            
            # 通道功率解析
            channel_powers = latest_record.get_all_channel_powers()
            print(f"通道功率 (前5个): {[f'{p:.0f}' for p in channel_powers[:5]]}")
            
            # 错误状态检查
            has_errors = latest_record.has_errors()
            print(f"是否有错误: {'是' if has_errors else '否'}")
            
            # 完整数据字典
            print(f"\n完整数据字典:")
            data_dict = latest_record.to_dict()
            print(f"原始数据字段数: {len(data_dict['raw_data'])}")
            print(f"解析数据字段数: {len(data_dict['parsed_data'])}")
        
        print("\n5. 数据统计示例")
        print("-" * 40)
        
        stats = time_series_service.get_data_statistics(device_id)
        print(f"总记录数: {stats.get('total_records', 0)}")
        print(f"最早记录: {stats.get('earliest_record', 'N/A')}")
        print(f"最新记录: {stats.get('latest_record', 'N/A')}")
        print(f"支持的数据字段: {', '.join(stats.get('data_fields', {}).keys())}")


def create_mock_hardware_data():
    """创建模拟的硬件数据包"""
    hardware_data = bytearray(80)
    
    pos = 0
    # 会话ID (2字节)
    hardware_data[pos:pos+2] = (0x1234).to_bytes(2, byteorder='little')
    pos += 2
    
    # 响应结果 (1字节) - 成功
    hardware_data[pos] = 0
    pos += 1
    
    # BL0910错误计数 (4字节)
    hardware_data[pos:pos+4] = (0).to_bytes(4, byteorder='little')
    pos += 4
    
    # 10个通道的BL0910 RMS寄存器值 (每个4字节)
    rms_values = [1600, 1620, 1580, 1610, 1590, 1605, 1615, 1585, 1595, 1600]
    for value in rms_values:
        hardware_data[pos:pos+4] = value.to_bytes(4, byteorder='little')
        pos += 4
    
    # 继电器状态 (2字节) - 前8个开启
    hardware_data[pos:pos+2] = (0b0000000011111111).to_bytes(2, byteorder='little')
    pos += 2
    
    # 短周期错误计数 (2字节)
    hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
    pos += 2
    
    # 长周期错误计数 (2字节)
    hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
    pos += 2
    
    # 最后零交叉时间 (4字节)
    hardware_data[pos:pos+4] = int(datetime.now().timestamp()).to_bytes(4, byteorder='little')
    pos += 4
    
    # 电压 (2字节, 已经 * 100) - 220.8V
    hardware_data[pos:pos+2] = (22080).to_bytes(2, byteorder='little')
    pos += 2
    
    # 温度 (2字节, 已经 * 100) - 36.2°C
    hardware_data[pos:pos+2] = (3620).to_bytes(2, byteorder='little')
    pos += 2
    
    # 总有功功率 (4字节, 已经 * 100) - 1520.5W
    hardware_data[pos:pos+4] = (152050).to_bytes(4, byteorder='little')
    pos += 4
    
    # 信号质量 (1字节)
    hardware_data[pos] = 28
    pos += 1
    
    # 误码率 (1字节)
    hardware_data[pos] = 1
    pos += 1
    
    # 继电器闭合故障 (2字节)
    hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
    pos += 2
    
    # 继电器分断故障 (2字节)
    hardware_data[pos:pos+2] = (0).to_bytes(2, byteorder='little')
    pos += 2
    
    return bytes(hardware_data)


def demonstrate_relay_parsing():
    """演示继电器状态解析"""
    print("\n" + "=" * 60)
    print("继电器状态解析示例")
    print("=" * 60)
    
    # 不同的继电器状态示例
    relay_examples = [
        ("全部关闭", 0b0000000000000000),
        ("全部开启", 0b1111111111111111),
        ("奇数开启", 0b0101010101010101),
        ("偶数开启", 0b1010101010101010),
        ("前8个开启", 0b0000000011111111),
        ("后8个开启", 0b1111111100000000),
    ]
    
    for description, relay_state in relay_examples:
        print(f"\n{description} (0b{relay_state:016b}):")
        
        # 创建临时记录用于解析
        record = TimeSeriesData(
            device_id="demo",
            timestamp=datetime.now(),
            relay_state=relay_state
        )
        
        relay_states = record.get_relay_states()
        active_relays = [k for k, v in relay_states.items() if v]
        
        if active_relays:
            print(f"  激活的继电器: {', '.join(active_relays)}")
        else:
            print("  没有激活的继电器")


def demonstrate_power_calculation():
    """演示功率计算示例"""
    print("\n" + "=" * 60)
    print("功率计算示例")
    print("=" * 60)
    
    # 模拟不同负载情况下的RMS值
    load_scenarios = [
        ("空载", [100, 105, 98, 102, 99, 101, 103, 97, 100, 104]),
        ("轻载", [500, 520, 480, 510, 490, 505, 515, 485, 495, 500]),
        ("中载", [1000, 1020, 980, 1010, 990, 1005, 1015, 985, 995, 1000]),
        ("重载", [1800, 1820, 1780, 1810, 1790, 1805, 1815, 1785, 1795, 1800]),
        ("过载", [2200, 2220, 2180, 2210, 2190, 2205, 2215, 2185, 2195, 2200]),
    ]
    
    for scenario, rms_values in load_scenarios:
        print(f"\n{scenario}情况:")
        
        record = TimeSeriesData(
            device_id="demo",
            timestamp=datetime.now(),
            bl0910_rms_values=rms_values
        )
        
        channel_powers = record.get_all_channel_powers()
        total_calculated = sum(p for p in channel_powers if p is not None)
        
        print(f"  各通道RMS值: {rms_values}")
        print(f"  各通道功率: {[f'{p:.0f}' if p else 'N/A' for p in channel_powers]}")
        print(f"  计算总功率: {total_calculated:.0f} (注: 需要实际的功率转换公式)")


if __name__ == "__main__":
    try:
        demonstrate_basic_usage()
        demonstrate_relay_parsing()
        demonstrate_power_calculation()
        
        print("\n" + "=" * 60)
        print("示例运行完成！")
        print("=" * 60)
        print("\n新的简化时序数据模型特点:")
        print("✅ 只存储原始硬件数据，节省存储空间")
        print("✅ 去除了复杂的JSON字段和batch表")
        print("✅ 在代码层面进行数据解析，灵活性更高")
        print("✅ 支持直接从硬件数据包写入")
        print("✅ 优化的索引策略，查询性能更好")
        print("✅ 支持继电器状态位解析")
        print("✅ 支持多通道功率数据处理")
        
    except Exception as e:
        print(f"运行示例时发生错误: {e}")
        import traceback
        traceback.print_exc()
