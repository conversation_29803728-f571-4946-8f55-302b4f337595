#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
调试信息解析服务模块
提供统一的调试信息解析接口，支持功率计算、继电器状态解析和故障状态处理
"""

import logging
from typing import Dict, Any, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
from models.device import Device
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


@dataclass
class ChannelState:
    """通道状态数据类"""
    channel: int
    enabled: bool
    status_text: str


@dataclass
class RelayStatus:
    """继电器状态数据类"""
    state_value: int
    state_hex: str
    channel_states: List[ChannelState]


@dataclass
class FaultStatus:
    """故障状态数据类"""
    relay_pull_fault: int
    relay_open_fault: int
    has_fault: bool
    fault_description: str


@dataclass
class SensorData:
    """传感器数据类"""
    voltage: Optional[float]
    temperature: Optional[float]
    total_power: Optional[float]
    csq: Optional[int]
    ber: Optional[int]
    zero_cross_time: Optional[int]


@dataclass
class ErrorCounts:
    """错误计数数据类"""
    bl0910_error_count: Optional[int]
    short_period_error_count: Optional[int]
    long_period_error_count: Optional[int]


@dataclass
class ParsedDebugData:
    """解析后的调试数据类"""
    channel_powers: List[Optional[float]]
    relay_status: RelayStatus
    sensor_data: SensorData
    error_counts: ErrorCounts
    fault_status: FaultStatus


@dataclass
class DebugInfoResponse:
    """调试信息响应数据类"""
    session_id: int
    result: int
    info: Dict[str, Any]
    timestamp: datetime
    parsed_data: ParsedDebugData


class PowerCalculator:
    """功率计算器"""
    
    # 充电桩设备的BL0910系数 (10个通道)
    BL0910_COEF_TYPE_50 = [
        [-0.00137, 0],                # MEASURE_1
        [-0.00137, 0],                # MEASURE_2
        [0.00137, 0],                 # MEASURE_3
        [0.00137, 0],                 # MEASURE_4
        [0.00137, -0.25],             # MEASURE_5
        [0.00137, 0],                 # MEASURE_6
        [0.00136, -0.15],             # MEASURE_7
        [-0.00137, -0.21],            # MEASURE_8
        [-0.00137, 0],                # MEASURE_9
        [0.00137, -0.16],             # MEASURE_10
    ]
    
    # 插座设备的BL0939系数 (只有两个通道)
    BL0939_COEF_TYPE_51 = [
        [0.0013326313871873173, -0.2570092159648129],  # MEASURE_1
        [0.0013520570083105743, -0.27744924955897493], # MEASURE_2
    ]
    
    @staticmethod
    def calculate_channel_powers(bl0910_rms_regs: List[int], device_type: int) -> List[Optional[float]]:
        """
        根据设备类型计算各通道功率值
        
        Args:
            bl0910_rms_regs: BL0910 RMS寄存器值列表
            device_type: 设备类型
            
        Returns:
            List[Optional[float]]: 各通道功率值列表
        """
        try:
            channel_powers = []
            
            if device_type == 51:  # 插座设备
                # 只处理前2个通道
                for i, reg_value in enumerate(bl0910_rms_regs[:2]):
                    # 将寄存器值视为int32_t整数
                    int32_value = reg_value & 0xFFFFFFFF
                    if int32_value & 0x80000000:
                        int32_value = int32_value - 0x100000000

                    # 应用线性变换: power = slope * register_value + intercept
                    power = PowerCalculator.BL0939_COEF_TYPE_51[i][0] * int32_value + PowerCalculator.BL0939_COEF_TYPE_51[i][1]
                    channel_powers.append(power)

                # 其余通道填充None
                channel_powers.extend([None] * 8)

            elif device_type == 50:  # 默认设备类型50 (充电桩)
                for i, reg_value in enumerate(bl0910_rms_regs):
                    if i < 10:  # 只处理前10个通道
                        # 将寄存器值视为int32_t整数
                        int32_value = reg_value & 0xFFFFFFFF
                        if int32_value & 0x80000000:
                            int32_value = int32_value - 0x100000000

                        # 应用线性变换: power = slope * register_value + intercept
                        power = PowerCalculator.BL0910_COEF_TYPE_50[i][0] * int32_value + PowerCalculator.BL0910_COEF_TYPE_50[i][1]
                        channel_powers.append(power)
                        
            elif device_type == 10:
                for i, reg_value in enumerate(bl0910_rms_regs):
                    if i < 10:  # 只处理前10个通道
                        # 将寄存器值视为int32_t整数
                        int32_value = reg_value & 0xFFFFFFFF
                        if int32_value & 0x80000000:
                            int32_value = int32_value - 0x100000000
                        RawADCCurrRMS = int32_value * 0.001
                        CurrVoltRMS = RawADCCurrRMS * (3.3 / 4096)
                        realCurrRMS = (10.58 * (CurrVoltRMS) - 0.001)
                        realPower = realCurrRMS * 220.0 * 0.7
                        channel_powers.append(realPower)
            else:
                logger.warning(f"未知设备类型: {device_type}，使用默认计算方式")
                # 使用默认的充电桩系数
                for i, reg_value in enumerate(bl0910_rms_regs):
                    if i < 10:
                        int32_value = reg_value & 0xFFFFFFFF
                        if int32_value & 0x80000000:
                            int32_value = int32_value - 0x100000000
                        power = PowerCalculator.BL0910_COEF_TYPE_50[i][0] * int32_value + PowerCalculator.BL0910_COEF_TYPE_50[i][1]
                        channel_powers.append(power)
                        
            return channel_powers
            
        except Exception as e:
            logger.error(f"计算功率值异常: {e}")
            return [None] * 10


class RelayStateParser:
    """继电器状态解析器"""
    
    @staticmethod
    def parse_relay_status(relay_state: Optional[int]) -> RelayStatus:
        """
        解析继电器状态
        
        Args:
            relay_state: 继电器状态值
            
        Returns:
            RelayStatus: 继电器状态详情
        """
        try:
            if relay_state is None:
                relay_state = 0
                
            state_hex = f"0x{relay_state:04X}"
            channel_states = []
            
            # 解析各通道状态位（10个通道）
            for i in range(10):
                enabled = bool(relay_state & (1 << i))
                status_text = "开启" if enabled else "关闭"
                channel_states.append(ChannelState(
                    channel=i + 1,
                    enabled=enabled,
                    status_text=status_text
                ))
                
            return RelayStatus(
                state_value=relay_state,
                state_hex=state_hex,
                channel_states=channel_states
            )
            
        except Exception as e:
            logger.error(f"解析继电器状态异常: {e}")
            return RelayStatus(
                state_value=0,
                state_hex="0x0000",
                channel_states=[ChannelState(i+1, False, "关闭") for i in range(10)]
            )


class FaultStatusParser:
    """故障状态解析器"""
    
    @staticmethod
    def parse_fault_status(relay_pull_fault: Optional[int], relay_open_fault: Optional[int]) -> FaultStatus:
        """
        解析故障状态
        
        Args:
            relay_pull_fault: 继电器闭合故障计数
            relay_open_fault: 继电器断开故障计数
            
        Returns:
            FaultStatus: 故障状态详情
        """
        try:
            pull_fault = relay_pull_fault or 0
            open_fault = relay_open_fault or 0
            
            has_fault = pull_fault > 0 or open_fault > 0
            
            fault_descriptions = []
            if pull_fault > 0:
                fault_descriptions.append(f"闭合故障码：{pull_fault:04X}")  
            if open_fault > 0:
                fault_descriptions.append(f"断开故障码：{open_fault:04X}")
                
            fault_description = "，".join(fault_descriptions) if fault_descriptions else "无故障"
            
            return FaultStatus(
                relay_pull_fault=pull_fault,
                relay_open_fault=open_fault,
                has_fault=has_fault,
                fault_description=fault_description
            )
            
        except Exception as e:
            logger.error(f"解析故障状态异常: {e}")
            return FaultStatus(
                relay_pull_fault=0,
                relay_open_fault=0,
                has_fault=False,
                fault_description="解析异常"
            )


class SensorDataProcessor:
    """传感器数据处理器"""
    
    @staticmethod
    def process_sensor_data(info: Dict[str, Any]) -> SensorData:
        """
        处理传感器数据
        
        Args:
            info: 调试信息字典
            
        Returns:
            SensorData: 传感器数据
        """
        try:
            # 电压值，除以100转换为实际值
            voltage = None
            if info.get('voltage') is not None:
                voltage = info.get('voltage', 0) / 100.0

            # 温度值，除以100转换为实际值
            temperature = None
            if info.get('temperature') is not None:
                temperature = info.get('temperature', 0) / 100.0

            # 总有功功率，除以100转换为实际值
            total_power = None
            if info.get('total_power') is not None:
                total_power = info.get('total_power', 0) / 100.0

            return SensorData(
                voltage=voltage,
                temperature=temperature,
                total_power=total_power,
                csq=info.get('csq'),
                ber=info.get('ber'),
                zero_cross_time=info.get('zero_cross_time')
            )
            
        except Exception as e:
            logger.error(f"处理传感器数据异常: {e}")
            return SensorData(
                voltage=None,
                temperature=None,
                total_power=None,
                csq=None,
                ber=None,
                zero_cross_time=None
            )


class DebugInfoParser:
    """调试信息解析器主类"""
    
    @staticmethod
    def parse_debug_info(raw_msg: Dict[str, Any], device: Device) -> DebugInfoResponse:
        """
        解析调试信息
        
        Args:
            raw_msg: 原始消息
            device: 设备对象
            
        Returns:
            DebugInfoResponse: 解析后的调试信息响应
        """
        try:
            # 解析基本响应数据
            parsed_data = raw_msg.get('parsed_data', {})
            session_id = parsed_data.get('session_id', 0)
            result = parsed_data.get('result', 1)
            info = parsed_data.get('info', {})
            
            if result != 0:
                logger.error(f"设备 {device.id} 返回错误: {result}")
                # 返回默认的空响应
                return DebugInfoParser._create_default_response(session_id, result, info)
            
            if not info:
                logger.error(f"设备 {device.id} 返回的调试信息不包含info字段")
                return DebugInfoParser._create_default_response(session_id, result, info)
            
            # 解析各个组件
            channel_powers = PowerCalculator.calculate_channel_powers(
                info.get('bl0910_rms_regs', []), 
                device.device_type or 50
            )
            
            relay_status = RelayStateParser.parse_relay_status(info.get('relay_state'))
            
            sensor_data = SensorDataProcessor.process_sensor_data(info)
            
            error_counts = ErrorCounts(
                bl0910_error_count=info.get('bl0910_error_count'),
                short_period_error_count=info.get('short_period_error_count'),
                long_period_error_count=info.get('long_period_error_count')
            )
            
            fault_status = FaultStatusParser.parse_fault_status(
                info.get('relay_pull_fault'),
                info.get('relay_open_fault')
            )
            
            parsed_debug_data = ParsedDebugData(
                channel_powers=channel_powers,
                relay_status=relay_status,
                sensor_data=sensor_data,
                error_counts=error_counts,
                fault_status=fault_status
            )
            
            return DebugInfoResponse(
                session_id=session_id,
                result=result,
                info=info,
                timestamp=datetime.now(),
                parsed_data=parsed_debug_data
            )
            
        except Exception as e:
            logger.error(f"解析调试信息异常: {e}")
            return DebugInfoParser._create_default_response(0, 1, {})
    
    @staticmethod
    def _create_default_response(session_id: int, result: int, info: Dict[str, Any]) -> DebugInfoResponse:
        """创建默认响应"""
        return DebugInfoResponse(
            session_id=session_id,
            result=result,
            info=info,
            timestamp=datetime.now(),
            parsed_data=ParsedDebugData(
                channel_powers=[None] * 10,
                relay_status=RelayStatus(
                    state_value=0,
                    state_hex="0x0000",
                    channel_states=[ChannelState(i+1, False, "关闭") for i in range(10)]
                ),
                sensor_data=SensorData(
                    voltage=None,
                    temperature=None,
                    total_power=None,
                    csq=None,
                    ber=None,
                    zero_cross_time=None
                ),
                error_counts=ErrorCounts(
                    bl0910_error_count=None,
                    short_period_error_count=None,
                    long_period_error_count=None
                ),
                fault_status=FaultStatus(
                    relay_pull_fault=0,
                    relay_open_fault=0,
                    has_fault=False,
                    fault_description="无数据"
                )
            )
        )
    
    @staticmethod
    def to_dict(debug_response: DebugInfoResponse) -> Dict[str, Any]:
        """
        将DebugInfoResponse转换为字典格式
        
        Args:
            debug_response: 调试信息响应对象
            
        Returns:
            Dict[str, Any]: 字典格式的数据
        """
        try:
            return {
                'session_id': debug_response.session_id,
                'result': debug_response.result,
                'info': debug_response.info,
                'timestamp': debug_response.timestamp.isoformat(),
                'parsed_data': {
                    'channel_powers': debug_response.parsed_data.channel_powers,
                    'relay_status': {
                        'state_value': debug_response.parsed_data.relay_status.state_value,
                        'state_hex': debug_response.parsed_data.relay_status.state_hex,
                        'channel_states': [
                            {
                                'channel': cs.channel,
                                'enabled': cs.enabled,
                                'status_text': cs.status_text
                            } for cs in debug_response.parsed_data.relay_status.channel_states
                        ]
                    },
                    'sensor_data': {
                        'voltage': debug_response.parsed_data.sensor_data.voltage,
                        'temperature': debug_response.parsed_data.sensor_data.temperature,
                        'total_power': debug_response.parsed_data.sensor_data.total_power,
                        'csq': debug_response.parsed_data.sensor_data.csq,
                        'ber': debug_response.parsed_data.sensor_data.ber,
                        'zero_cross_time': debug_response.parsed_data.sensor_data.zero_cross_time
                    },
                    'error_counts': {
                        'bl0910_error_count': debug_response.parsed_data.error_counts.bl0910_error_count,
                        'short_period_error_count': debug_response.parsed_data.error_counts.short_period_error_count,
                        'long_period_error_count': debug_response.parsed_data.error_counts.long_period_error_count
                    },
                    'fault_status': {
                        'relay_pull_fault': debug_response.parsed_data.fault_status.relay_pull_fault,
                        'relay_open_fault': debug_response.parsed_data.fault_status.relay_open_fault,
                        'has_fault': debug_response.parsed_data.fault_status.has_fault,
                        'fault_description': debug_response.parsed_data.fault_status.fault_description
                    }
                }
            }
        except Exception as e:
            logger.error(f"转换调试信息为字典异常: {e}")
            return {
                'session_id': 0,
                'result': 1,
                'info': {},
                'timestamp': datetime.now().isoformat(),
                'parsed_data': None,
                'error': str(e)
            }