<!-- 固件信息悬停卡片模板（隐藏） -->
<template id="firmwareInfoCardTemplate">
    <div class="firmware-info-card">
        <div class="card-header">
            <div class="card-title">
                <i class="fas fa-microchip"></i>
                <span class="card-firmware-name">固件信息</span>
            </div>
            <div class="card-close">
                <i class="fas fa-times"></i>
            </div>
        </div>
        <div class="card-body">
            <!-- 加载状态 -->
            <div class="firmware-loading">
                <div class="spinner-border text-primary" role="status">
                    <span class="visually-hidden">加载中...</span>
                </div>
                <div class="mt-2">正在获取固件信息...</div>
            </div>

            <!-- 错误状态 -->
            <div class="firmware-error" style="display: none;">
                <i class="fas fa-exclamation-triangle fa-2x mb-2 text-warning"></i>
                <div>获取固件信息失败</div>
                <div class="small mt-1 error-message text-muted"></div>
            </div>

            <!-- 固件信息内容 -->
            <div class="firmware-content" style="display: none;">
                <!-- 基本信息 -->
                <div class="firmware-section">
                    <h6 class="section-title">
                        <i class="fas fa-info-circle me-1"></i>基本信息
                    </h6>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">固件名称:</span>
                            <span class="info-value firmware-name">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">版本号:</span>
                            <span class="info-value firmware-version">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">CRC32:</span>
                            <span class="info-value firmware-crc32">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">设备类型:</span>
                            <span class="info-value firmware-device-type">--</span>
                        </div>
                    </div>
                </div>

                <!-- 文件信息 -->
                <div class="firmware-section">
                    <h6 class="section-title">
                        <i class="fas fa-file me-1"></i>文件信息
                    </h6>
                    <div class="info-grid">
                        <div class="info-item">
                            <span class="info-label">文件大小:</span>
                            <span class="info-value firmware-size">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">文件名:</span>
                            <span class="info-value firmware-filename">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">上传时间:</span>
                            <span class="info-value firmware-upload-time">--</span>
                        </div>
                        <div class="info-item">
                            <span class="info-label">下载次数:</span>
                            <span class="info-value firmware-download-count">--</span>
                        </div>
                    </div>
                </div>

                <!-- 描述信息 -->
                <div class="firmware-section">
                    <h6 class="section-title">
                        <i class="fas fa-align-left me-1"></i>描述信息
                    </h6>
                    <div class="firmware-description">
                        <p class="description-text">--</p>
                    </div>
                </div>

                <!-- 操作按钮 -->
                <div class="firmware-actions">
                    <button type="button" class="btn btn-sm btn-primary download-btn" title="下载固件">
                        <i class="fas fa-download me-1"></i>下载
                    </button>
                    <button type="button" class="btn btn-sm btn-info details-btn" title="查看详情">
                        <i class="fas fa-eye me-1"></i>详情
                    </button>
                </div>
            </div>
        </div>
    </div>
</template>

<style>
    /* 固件信息卡片样式 */
    .firmware-info-card {
        position: fixed;
        background: white;
        border: 1px solid #e0e0e0;
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
        z-index: 10000;
        width: 380px;
        max-height: 500px;
        overflow: hidden;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
        display: none;
        animation: fadeInUp 0.2s ease-out;
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(10px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    .firmware-info-card .card-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 12px 16px;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: none;
    }

    .firmware-info-card .card-title {
        display: flex;
        align-items: center;
        font-weight: 600;
        font-size: 14px;
    }

    .firmware-info-card .card-title i {
        margin-right: 8px;
        font-size: 16px;
    }

    .firmware-info-card .card-close {
        cursor: pointer;
        opacity: 0.8;
        transition: opacity 0.2s;
        padding: 4px;
        border-radius: 4px;
    }

    .firmware-info-card .card-close:hover {
        opacity: 1;
        background: rgba(255, 255, 255, 0.1);
    }

    .firmware-info-card .card-body {
        padding: 16px;
        max-height: 420px;
        overflow-y: auto;
    }

    /* 加载和错误状态 */
    .firmware-info-card .firmware-loading,
    .firmware-info-card .firmware-error {
        text-align: center;
        padding: 20px;
        color: #666;
    }

    .firmware-info-card .firmware-error i {
        color: #ffc107;
    }

    /* 固件信息内容 */
    .firmware-info-card .firmware-section {
        margin-bottom: 20px;
    }

    .firmware-info-card .firmware-section:last-child {
        margin-bottom: 0;
    }

    .firmware-info-card .section-title {
        font-size: 13px;
        font-weight: 600;
        color: #495057;
        margin-bottom: 12px;
        padding-bottom: 6px;
        border-bottom: 1px solid #f0f0f0;
        display: flex;
        align-items: center;
    }

    .firmware-info-card .section-title i {
        color: #667eea;
    }

    .firmware-info-card .info-grid {
        display: grid;
        gap: 8px;
    }

    .firmware-info-card .info-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 6px 0;
    }

    .firmware-info-card .info-label {
        font-size: 12px;
        color: #6c757d;
        font-weight: 500;
        min-width: 80px;
    }

    .firmware-info-card .info-value {
        font-size: 12px;
        color: #495057;
        font-weight: 600;
        text-align: right;
        flex: 1;
        margin-left: 12px;
        word-break: break-all;
    }

    .firmware-info-card .firmware-description {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 12px;
    }

    .firmware-info-card .description-text {
        font-size: 12px;
        color: #495057;
        margin: 0;
        line-height: 1.4;
    }

    /* 操作按钮 */
    .firmware-info-card .firmware-actions {
        display: flex;
        gap: 8px;
        margin-top: 16px;
        padding-top: 16px;
        border-top: 1px solid #f0f0f0;
    }

    .firmware-info-card .firmware-actions .btn {
        flex: 1;
        font-size: 12px;
        padding: 6px 12px;
        border-radius: 6px;
        transition: all 0.2s;
    }

    .firmware-info-card .firmware-actions .btn:hover {
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    /* 响应式设计 */
    @media (max-width: 480px) {
        .firmware-info-card {
            width: 320px;
            max-height: 400px;
        }
        
        .firmware-info-card .card-body {
            padding: 12px;
        }
    }

    /* 滚动条样式 */
    .firmware-info-card .card-body::-webkit-scrollbar {
        width: 4px;
    }

    .firmware-info-card .card-body::-webkit-scrollbar-track {
        background: #f1f1f1;
        border-radius: 2px;
    }

    .firmware-info-card .card-body::-webkit-scrollbar-thumb {
        background: #c1c1c1;
        border-radius: 2px;
    }

    .firmware-info-card .card-body::-webkit-scrollbar-thumb:hover {
        background: #a8a8a8;
    }
</style>

<script>
    // 固件信息卡片管理
    let firmwareInfoCard = null;
    let hideCardTimeout = null;
    let currentCrc32 = null;

    // 显示固件信息卡片
    function showFirmwareInfoCard(crc32, targetElement) {
        // 如果CRC32相同且卡片已显示，则不重复加载
        if (currentCrc32 === crc32 && firmwareInfoCard && firmwareInfoCard.style.display !== 'none') {
            return;
        }

        currentCrc32 = crc32;

        // 清除隐藏定时器
        if (hideCardTimeout) {
            clearTimeout(hideCardTimeout);
            hideCardTimeout = null;
        }

        // 创建卡片元素（如果不存在）
        if (!firmwareInfoCard) {
            const template = document.getElementById('firmwareInfoCardTemplate');
            if (!template) {
                console.error('固件信息卡片模板未找到');
                return;
            }

            firmwareInfoCard = template.content.cloneNode(true).firstElementChild;
            document.body.appendChild(firmwareInfoCard);

            // 添加关闭按钮事件
            const closeBtn = firmwareInfoCard.querySelector('.card-close');
            closeBtn.addEventListener('click', hideFirmwareInfoCard);

            // 添加鼠标事件防止卡片消失
            firmwareInfoCard.addEventListener('mouseenter', function () {
                if (hideCardTimeout) {
                    clearTimeout(hideCardTimeout);
                    hideCardTimeout = null;
                }
            });

            firmwareInfoCard.addEventListener('mouseleave', function () {
                hideFirmwareInfoCard();
            });

            // 添加操作按钮事件
            const downloadBtn = firmwareInfoCard.querySelector('.download-btn');
            const detailsBtn = firmwareInfoCard.querySelector('.details-btn');

            downloadBtn.addEventListener('click', function() {
                const firmwareId = firmwareInfoCard.dataset.firmwareId;
                if (firmwareId) {
                    window.open(`/firmware/download/${firmwareId}`, '_blank');
                }
            });

            detailsBtn.addEventListener('click', function() {
                // 跳转到固件管理页面
                window.open('/firmware_list', '_blank');
            });
        }

        // 重置卡片状态
        resetFirmwareCardState();

        // 定位卡片
        positionFirmwareCard(targetElement);

        // 显示卡片
        firmwareInfoCard.style.display = 'block';

        // 加载固件信息
        loadFirmwareInfo(crc32);
    }

    // 隐藏固件信息卡片
    function hideFirmwareInfoCard() {
        if (hideCardTimeout) {
            clearTimeout(hideCardTimeout);
        }

        hideCardTimeout = setTimeout(() => {
            if (firmwareInfoCard) {
                firmwareInfoCard.style.display = 'none';
                currentCrc32 = null;
            }
        }, 200);
    }

    // 重置卡片状态
    function resetFirmwareCardState() {
        const loadingElement = firmwareInfoCard.querySelector('.firmware-loading');
        const errorElement = firmwareInfoCard.querySelector('.firmware-error');
        const contentElement = firmwareInfoCard.querySelector('.firmware-content');

        loadingElement.style.display = 'block';
        errorElement.style.display = 'none';
        contentElement.style.display = 'none';
    }

    // 定位卡片
    function positionFirmwareCard(targetElement) {
        const rect = targetElement.getBoundingClientRect();
        const cardWidth = 380;
        const cardHeight = 500;

        let left = rect.right + 10;
        let top = rect.top;

        // 检查右侧空间
        if (left + cardWidth > window.innerWidth) {
            left = rect.left - cardWidth - 10;
        }

        // 检查底部空间
        if (top + cardHeight > window.innerHeight) {
            top = window.innerHeight - cardHeight - 10;
        }

        // 确保不超出屏幕边界
        left = Math.max(10, left);
        top = Math.max(10, top);

        firmwareInfoCard.style.left = left + 'px';
        firmwareInfoCard.style.top = top + 'px';
    }

    // 加载固件信息
    function loadFirmwareInfo(crc32) {
        const loadingElement = firmwareInfoCard.querySelector('.firmware-loading');
        const errorElement = firmwareInfoCard.querySelector('.firmware-error');
        const contentElement = firmwareInfoCard.querySelector('.firmware-content');

        // 调用API获取固件信息
        fetch(`/api/firmware/by_crc32/${crc32}`)
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (!data.success) {
                    throw new Error(data.message || '获取固件信息失败');
                }

                // 更新卡片内容
                updateFirmwareCardContent(data.firmware);

                // 显示内容
                loadingElement.style.display = 'none';
                contentElement.style.display = 'block';
            })
            .catch(error => {
                console.error('获取固件信息失败:', error);

                // 显示错误状态
                loadingElement.style.display = 'none';
                errorElement.style.display = 'block';

                const errorMessage = firmwareInfoCard.querySelector('.error-message');
                errorMessage.textContent = error.message || '未知错误';
            });
    }

    // 更新固件卡片内容
    function updateFirmwareCardContent(firmware) {
        // 更新标题
        const cardTitle = firmwareInfoCard.querySelector('.card-firmware-name');
        cardTitle.textContent = `${firmware.name} v${firmware.version}`;

        // 更新基本信息
        firmwareInfoCard.querySelector('.firmware-name').textContent = firmware.name;
        firmwareInfoCard.querySelector('.firmware-version').textContent = `v${firmware.version}`;
        firmwareInfoCard.querySelector('.firmware-crc32').textContent = firmware.crc32;
        firmwareInfoCard.querySelector('.firmware-device-type').textContent = firmware.device_type_name;

        // 更新文件信息
        firmwareInfoCard.querySelector('.firmware-size').textContent = firmware.size_formatted;
        firmwareInfoCard.querySelector('.firmware-filename').textContent = firmware.file_path;
        firmwareInfoCard.querySelector('.firmware-upload-time').textContent = firmware.upload_time;
        firmwareInfoCard.querySelector('.firmware-download-count').textContent = `${firmware.download_count} 次`;

        // 更新描述
        firmwareInfoCard.querySelector('.description-text').textContent = firmware.description;

        // 保存固件ID用于下载
        firmwareInfoCard.dataset.firmwareId = firmware.id;
    }

    // 全局函数，供外部调用
    window.showFirmwareInfoCard = showFirmwareInfoCard;
    window.hideFirmwareInfoCard = hideFirmwareInfoCard;
</script>
