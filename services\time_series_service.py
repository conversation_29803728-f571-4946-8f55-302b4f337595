#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的时序数据库服务
用于高效存储和查询设备原始硬件数据
"""

import os
import json
import logging
from datetime import datetime, timedelta
from typing import Any, Optional, Union
from sqlalchemy import and_, or_, func, text
from sqlalchemy.exc import SQLAlchemyError
from flask import current_app

from models.database import db
from models.time_series_data import TimeSeriesData
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()


class TimeSeriesService:
    """时序数据库服务类"""

    def __init__(self):
        self.batch_size = 1000  # 批量插入大小
        self.cache_ttl = 300    # 缓存TTL（秒）
        self._cache = {}        # 简单内存缓存

    def write_sensor_data(self, device_id: str,
                         bl0910_error_count: int = None,
                         bl0910_rms_values: list[int] = None,
                         relay_state: int = None,
                         short_period_error_count: int = None,
                         long_period_error_count: int = None,
                         last_zero_cross_time: int = None,
                         voltage: float = None,
                         temperature: float = None,
                         total_power: float = None,
                         csq: int = None,
                         ber: int = None,
                         relay_pull_fault: int = None,
                         relay_open_fault: int = None) -> bool:
        """
        写入传感器原始数据到时序数据库
        基于HandleHardwareInfoQuery函数的数据结构

        Args:
            device_id: 设备ID
            bl0910_error_count: BL0910错误计数
            bl0910_rms_values: 10个通道的BL0910 RMS寄存器值列表
            relay_state: 继电器状态 (16位)
            short_period_error_count: 短周期错误计数
            long_period_error_count: 长周期错误计数
            last_zero_cross_time: 最后零交叉时间
            voltage: 电压值 (将被转换为原始值 * 100)
            temperature: 温度值 (将被转换为原始值 * 100)
            total_power: 总功率 (将被转换为原始值 * 100)
            csq: 信号质量
            ber: 误码率
            relay_pull_fault: 继电器闭合故障
            relay_open_fault: 继电器断开故障

        Returns:
            bool: 写入是否成功
        """
        try:
            current_time = datetime.now()

            # 处理BL0910 RMS值数组，确保长度为10
            processed_rms_values = None
            if bl0910_rms_values:
                processed_rms_values = bl0910_rms_values[:10]  # 只取前10个
                # 如果不足10个，用0填充
                while len(processed_rms_values) < 10:
                    processed_rms_values.append(0)

            # 转换浮点数为原始整数值 (乘以100)
            voltage_raw = int(voltage * 100) if voltage is not None else None
            temperature_raw = int(temperature * 100) if temperature is not None else None
            total_power_raw = int(total_power * 100) if total_power is not None else None

            # 创建时序数据记录
            time_series_record = TimeSeriesData(
                device_id=device_id,
                timestamp=current_time,
                bl0910_error_count=bl0910_error_count,
                bl0910_rms_values=processed_rms_values,
                relay_state=relay_state,
                short_period_error_count=short_period_error_count,
                long_period_error_count=long_period_error_count,
                last_zero_cross_time=last_zero_cross_time,
                voltage_raw=voltage_raw,
                temperature_raw=temperature_raw,
                total_power_raw=total_power_raw,
                csq=csq,
                ber=ber,
                relay_pull_fault=relay_pull_fault,
                relay_open_fault=relay_open_fault
            )

            # 插入数据
            db.session.add(time_series_record)
            db.session.commit()

            logger.info(f"成功写入设备 {device_id} 的时序数据")
            return True

        except SQLAlchemyError as e:
            db.session.rollback()
            logger.error(f"写入时序数据异常: {e}")
            return False
        except Exception as e:
            db.session.rollback()
            logger.error(f"写入时序数据异常: {e}")
            return False

    def write_hardware_info_data(self, device_id: str, hardware_data: bytes) -> bool:
        """
        直接从硬件信息查询响应数据写入时序数据库
        解析基于HandleHardwareInfoQuery函数的响应格式

        Args:
            device_id: 设备ID
            hardware_data: 硬件信息查询的响应数据 (bytes)

        Returns:
            bool: 写入是否成功
        """
        try:
            if len(hardware_data) < 50:  # 检查数据长度是否足够
                logger.error(f"硬件数据长度不足: {len(hardware_data)} bytes")
                return False

            pos = 2  # 跳过会话ID (2字节)

            # 检查响应结果
            if hardware_data[pos] != 0:  # RESPONSE_RESULT_SUCCESS = 0
                logger.error(f"硬件查询响应失败: {hardware_data[pos]}")
                return False
            pos += 1

            # 解析BL0910错误计数 (4字节)
            bl0910_error_count = int.from_bytes(hardware_data[pos:pos+4], byteorder='little')
            pos += 4

            # 解析10个通道的BL0910 RMS寄存器值 (每个4字节)
            bl0910_rms_values = []
            for i in range(10):
                rms_value = int.from_bytes(hardware_data[pos:pos+4], byteorder='little')
                bl0910_rms_values.append(rms_value)
                pos += 4

            # 解析继电器状态 (2字节)
            relay_state = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2

            # 解析周期错误计数 (各2字节)
            short_period_error_count = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2
            long_period_error_count = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2

            # 解析最后零交叉时间 (4字节)
            last_zero_cross_time = int.from_bytes(hardware_data[pos:pos+4], byteorder='little')
            pos += 4

            # 解析电压 (2字节, 原始值已经 * 100)
            voltage_raw = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2

            # 解析温度 (2字节, 原始值已经 * 100)
            temperature_raw = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2

            # 解析总有功功率 (4字节, 原始值已经 * 100)
            total_power_raw = int.from_bytes(hardware_data[pos:pos+4], byteorder='little')
            pos += 4

            # 解析信号质量 (各1字节)
            csq = hardware_data[pos]
            pos += 1
            ber = hardware_data[pos]
            pos += 1

            # 解析继电器故障状态 (各2字节)
            relay_pull_fault = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2
            relay_open_fault = int.from_bytes(hardware_data[pos:pos+2], byteorder='little')
            pos += 2

            # 创建时序数据记录
            current_time = datetime.now()
            time_series_record = TimeSeriesData(
                device_id=device_id,
                timestamp=current_time,
                bl0910_error_count=bl0910_error_count,
                bl0910_rms_values=bl0910_rms_values,
                relay_state=relay_state,
                short_period_error_count=short_period_error_count,
                long_period_error_count=long_period_error_count,
                last_zero_cross_time=last_zero_cross_time,
                voltage_raw=voltage_raw,
                temperature_raw=temperature_raw,
                total_power_raw=total_power_raw,
                csq=csq,
                ber=ber,
                relay_pull_fault=relay_pull_fault,
                relay_open_fault=relay_open_fault
            )

            # 插入数据
            db.session.add(time_series_record)
            db.session.commit()

            logger.info(f"成功从硬件数据写入设备 {device_id} 的时序数据")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"解析硬件数据异常: {e}")
            return False

    def query_power_data(self, device_id: str, start_time: datetime,
                        end_time: Optional[datetime] = None) -> dict[str, list[dict[str, Any]]]:
        """
        查询设备功率数据

        Args:
            device_id: 设备ID
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            Dict: 包含各通道功率数据的字典
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 生成缓存键
            cache_key = f"power_{device_id}_{start_time.date()}_{end_time.date()}"

            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 查询时序数据
            power_data = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time,
                    TimeSeriesData.bl0910_rms_values.isnot(None)
                )
            ).order_by(TimeSeriesData.timestamp).all()

            # 组织数据格式
            result_data = {f"channel_{i+1}": [] for i in range(10)}

            for record in power_data:
                # 获取所有通道的功率值
                channel_powers = record.get_all_channel_powers()

                for i, power in enumerate(channel_powers):
                    if power is not None:
                        channel_key = f"channel_{i+1}"
                        result_data[channel_key].append({
                            "time": record.timestamp.isoformat(),
                            "value": power
                        })

            # 缓存结果
            self._set_cache(cache_key, result_data)

            return result_data

        except Exception as e:
            logger.error(f"查询功率数据异常: {e}")
            return {}

    def query_single_value_data(self, device_id: str, data_type: str,
                               start_time: datetime, end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """
        查询单值数据（温度、电压等）

        Args:
            device_id: 设备ID
            data_type: 数据类型 ('temperature', 'voltage', 'total_power')
            start_time: 开始时间
            end_time: 结束时间

        Returns:
            List: 包含时序数据的列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 生成缓存键
            cache_key = f"{data_type}_{device_id}_{start_time.date()}_{end_time.date()}"

            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 查询数据
            data_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).all()

            # 组织数据格式
            result_data = []
            for record in data_records:
                value = None

                # 根据数据类型获取相应的值
                if data_type == 'temperature':
                    value = record.get_temperature()
                elif data_type == 'voltage':
                    value = record.get_voltage()
                elif data_type == 'total_power':
                    value = record.get_total_power()

                if value is not None:
                    result_data.append({
                        "time": record.timestamp.isoformat(),
                        "value": value
                    })

            # 缓存结果
            self._set_cache(cache_key, result_data)

            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据异常: {e}")
            return []

    def query_temperature_data(self, device_id: str, start_time: datetime,
                              end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """查询设备温度数据"""
        return self.query_single_value_data(device_id, 'temperature', start_time, end_time)

    def query_voltage_data(self, device_id: str, start_time: datetime,
                          end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """查询设备电压数据"""
        return self.query_single_value_data(device_id, 'voltage', start_time, end_time)

    def query_total_power_data(self, device_id: str, start_time: datetime,
                              end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """查询设备总功率数据"""
        return self.query_single_value_data(device_id, 'total_power', start_time, end_time)

    def query_csq_data(self, device_id: str, start_time: datetime,
                      end_time: Optional[datetime] = None) -> list[dict[str, Any]]:
        """
        查询设备信号质量数据

        Returns:
            List: 包含CSQ和BER数据的列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 生成缓存键
            cache_key = f"csq_{device_id}_{start_time.date()}_{end_time.date()}"

            # 检查缓存
            cached_data = self._get_from_cache(cache_key)
            if cached_data is not None:
                return cached_data

            # 查询信号质量数据
            csq_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time,
                    or_(TimeSeriesData.csq.isnot(None), TimeSeriesData.ber.isnot(None))
                )
            ).order_by(TimeSeriesData.timestamp).all()

            # 组织数据格式
            result_data = []
            for record in csq_records:
                if record.csq is not None:
                    result_data.append({
                        "time": record.timestamp.isoformat(),
                        "value": record.csq,
                        "ber": record.ber or 99  # BER默认值99表示未知
                    })

            # 缓存结果
            self._set_cache(cache_key, result_data)

            return result_data

        except Exception as e:
            logger.error(f"查询信号质量数据异常: {e}")
            return []

    def delete_old_data(self, days_to_keep: int = 30) -> bool:
        """
        删除旧数据，保留指定天数的数据

        Args:
            days_to_keep: 保留的天数

        Returns:
            bool: 删除是否成功
        """
        try:
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)

            # 删除旧的时序数据
            deleted_count = db.session.query(TimeSeriesData).filter(
                TimeSeriesData.timestamp < cutoff_date
            ).delete()

            db.session.commit()

            logger.info(f"删除了 {deleted_count} 条时序数据")
            return True

        except Exception as e:
            db.session.rollback()
            logger.error(f"删除旧数据异常: {e}")
            return False

    def get_data_statistics(self, device_id: str = None) -> dict[str, Any]:
        """
        获取数据统计信息

        Args:
            device_id: 设备ID，如果为None则统计所有设备

        Returns:
            Dict: 统计信息
        """
        try:
            query = db.session.query(
                func.count(TimeSeriesData.id).label('count'),
                func.min(TimeSeriesData.timestamp).label('earliest'),
                func.max(TimeSeriesData.timestamp).label('latest')
            )

            if device_id:
                query = query.filter(TimeSeriesData.device_id == device_id)

            stats = query.first()

            result = {
                'total_records': stats.count if stats else 0,
                'earliest_record': stats.earliest.isoformat() if stats and stats.earliest else None,
                'latest_record': stats.latest.isoformat() if stats and stats.latest else None,
                'data_fields': {
                    'voltage': 'voltage_raw',
                    'temperature': 'temperature_raw',
                    'total_power': 'total_power_raw',
                    'power_channels': 'bl0910_rms_values',
                    'signal_quality': 'csq, ber',
                    'relay_state': 'relay_state',
                    'errors': 'bl0910_error_count, short_period_error_count, long_period_error_count'
                }
            }

            return result

        except Exception as e:
            logger.error(f"获取数据统计异常: {e}")
            return {}

    def _get_from_cache(self, key: str) -> Any:
        """从缓存获取数据"""
        if key in self._cache:
            cache_entry = self._cache[key]
            if datetime.now() - cache_entry['timestamp'] < timedelta(seconds=self.cache_ttl):
                return cache_entry['data']
            else:
                del self._cache[key]
        return None

    def _set_cache(self, key: str, data: Any) -> None:
        """设置缓存数据"""
        self._cache[key] = {
            'data': data,
            'timestamp': datetime.now()
        }

        # 简单的缓存清理，防止内存泄漏
        if len(self._cache) > 100:
            # 删除最旧的缓存项
            oldest_key = min(self._cache.keys(), key=lambda k: self._cache[k]['timestamp'])
            del self._cache[oldest_key]


    def query_data_with_pagination(self, device_id: str, data_type: str,
                                  start_time: datetime, end_time: Optional[datetime] = None,
                                  page: int = 1, page_size: int = 1000) -> dict[str, Any]:
        """
        分页查询时序数据

        Args:
            device_id: 设备ID
            data_type: 数据类型 ('temperature', 'voltage', 'total_power')
            start_time: 开始时间
            end_time: 结束时间
            page: 页码（从1开始）
            page_size: 每页大小

        Returns:
            Dict: 包含数据和分页信息的字典
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 计算偏移量
            offset = (page - 1) * page_size

            # 查询总数
            total_count = db.session.query(func.count(TimeSeriesData.id)).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).scalar()

            # 查询数据
            data_records = db.session.query(TimeSeriesData).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time
                )
            ).order_by(TimeSeriesData.timestamp).offset(offset).limit(page_size).all()

            # 组织数据格式
            result_data = []
            for record in data_records:
                value = None

                # 根据数据类型获取相应的值
                if data_type == 'temperature':
                    value = record.get_temperature()
                elif data_type == 'voltage':
                    value = record.get_voltage()
                elif data_type == 'total_power':
                    value = record.get_total_power()

                if value is not None:
                    result_data.append({
                        "time": record.timestamp.isoformat(),
                        "value": value
                    })

            # 计算分页信息
            total_pages = (total_count + page_size - 1) // page_size

            return {
                'data': result_data,
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': total_count,
                    'total_pages': total_pages,
                    'has_next': page < total_pages,
                    'has_prev': page > 1
                }
            }

        except Exception as e:
            logger.error(f"分页查询{data_type}数据异常: {e}")
            return {
                'data': [],
                'pagination': {
                    'page': page,
                    'page_size': page_size,
                    'total_count': 0,
                    'total_pages': 0,
                    'has_next': False,
                    'has_prev': False
                }
            }

    def query_data_summary(self, device_id: str, data_type: str,
                          start_time: datetime, end_time: Optional[datetime] = None,
                          interval_minutes: int = 60) -> list[dict[str, Any]]:
        """
        查询数据摘要（按时间间隔聚合）

        Args:
            device_id: 设备ID
            data_type: 数据类型 ('temperature', 'voltage', 'total_power')
            start_time: 开始时间
            end_time: 结束时间
            interval_minutes: 聚合间隔（分钟）

        Returns:
            List: 聚合后的数据列表
        """
        try:
            if end_time is None:
                end_time = datetime.now()

            # 使用PostgreSQL的date_trunc函数进行时间聚合
            interval_expr = text(
                f"date_trunc('hour', timestamp) + "
                f"interval '{interval_minutes} minutes' * "
                f"floor(extract(minute from timestamp) / {interval_minutes})"
            )

            # 根据数据类型选择聚合字段
            value_field = None
            if data_type == 'temperature':
                value_field = TimeSeriesData.temperature_raw
            elif data_type == 'voltage':
                value_field = TimeSeriesData.voltage_raw
            elif data_type == 'total_power':
                value_field = TimeSeriesData.total_power_raw
            else:
                logger.error(f"不支持的数据类型: {data_type}")
                return []

            # 查询聚合数据
            aggregated_data = db.session.query(
                interval_expr.label('time_bucket'),
                func.avg(value_field).label('avg_value'),
                func.min(value_field).label('min_value'),
                func.max(value_field).label('max_value'),
                func.count(TimeSeriesData.id).label('count')
            ).filter(
                and_(
                    TimeSeriesData.device_id == device_id,
                    TimeSeriesData.timestamp >= start_time,
                    TimeSeriesData.timestamp <= end_time,
                    value_field.isnot(None)
                )
            ).group_by(interval_expr).order_by(interval_expr).all()

            # 组织数据格式，转换原始值为实际值
            result_data = []
            for record in aggregated_data:
                # 将原始值转换为实际值（除以100）
                avg_val = float(record.avg_value) / 100.0 if record.avg_value else None
                min_val = float(record.min_value) / 100.0 if record.min_value else None
                max_val = float(record.max_value) / 100.0 if record.max_value else None

                result_data.append({
                    "time": record.time_bucket.isoformat(),
                    "avg_value": avg_val,
                    "min_value": min_val,
                    "max_value": max_val,
                    "count": record.count
                })

            return result_data

        except Exception as e:
            logger.error(f"查询{data_type}数据摘要异常: {e}")
            return []


# 创建全局实例
time_series_service = TimeSeriesService()
