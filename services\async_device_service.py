"""
异步设备服务
提供设备参数查询、错误计数、调试信息等异步查询功能
"""

import logging
from datetime import datetime
from models.device import Device
from iot_client.bin_block.reg_addr import RegAddr
from services.iot_client_manager import IoTClientManager
from iot_client.functions.register_manager import RegisterManager
from services.debug_info_parser import DebugInfoParser
from models.database import db

# 导入格式化函数（从device_parameters模块）
def format_register_value_for_display(reg_addr, raw_value):
    """格式化寄存器值用于前端显示"""
    try:
        value = int(raw_value) if raw_value is not None else 0

        # 使用if-elif结构替代match，避免引用不存在的寄存器
        if reg_addr == RegAddr.REG_VERSION_H:
            pcb_version = (value >> 8) & 0xFF
            pcb_version = "新版BL0910" if pcb_version >= 50 else "旧版霍尔"
            fw_version = value & 0xFF
            return f"PCB:{pcb_version},FW Major:{fw_version}"
        elif reg_addr == RegAddr.REG_VERSION_L:
            minor = (value >> 8) & 0xFF
            patch = value & 0xFF
            return f"FW Minor:{minor}.{patch}"
        elif reg_addr == RegAddr.REG_CTRL1:
            # 解析控制寄存器的各个位
            sim_mode = (value >> 0) & 0x01
            led_mode = (value >> 1) & 0x01
            wtc_map_req = (value >> 2) & 0x01
            wtc_timeout_stop = (value >> 3) & 0x01

            sim_status = "SIM卡拔出无操作" if sim_mode == 1 else "SIM卡拔出启动所有口"
            led_status = "LED闪烁" if led_mode == 1 else "LED呼吸灯"

            result = f"{value} (SIM:{sim_status}, LED:{led_status}"
            if wtc_map_req:
                result += ", 无线充电编号获取:开启"
            if wtc_timeout_stop:
                result += ", 无线充电超时停止:开启"
            result += ")"
            return result
        elif reg_addr == RegAddr.REG_PERSENTAGE:
            # 百分比需要除以100
            percentage = value / 100.0
            return f"{value} ({percentage:.1f}%)"
        elif reg_addr == RegAddr.REG_CSQ:
            # CSQ信号质量特殊解析：g_pSysInfo->reg[REG_CSQ] = (uint16_t)(csq << 8) | ber;
            csq = (value >> 8) & 0xFF  # 高字节是CSQ
            ber = value & 0xFF         # 低字节是BER

            # CSQ信号质量等级判断
            if csq == 99:
                csq_desc = "未知"
            elif csq >= 20:
                csq_desc = "优秀"
            elif csq >= 15:
                csq_desc = "良好"
            elif csq >= 10:
                csq_desc = "一般"
            elif csq >= 5:
                csq_desc = "较差"
            else:
                csq_desc = "很差"

            return f"{value} (CSQ:{csq}({csq_desc}), BER:{ber})"
        elif reg_addr == RegAddr.REG_ERROR_CNT1:
            # 错误计数器1：高字节为服务器掉线次数，低字节为SIM卡被拔出的次数
            server_offline_count = (value >> 8) & 0xFF
            sim_pull_count = value & 0xFF
            return f"{value} (服务器掉线:{server_offline_count}次, SIM卡拔出:{sim_pull_count}次)"
        elif reg_addr == RegAddr.REG_ERROR_CNT2:
            # 错误计数器2：高字节为电压过零中断周期小于工频周期的次数，低字节为电压过零中断周期大于工频周期的次数
            short_period_count = (value >> 8) & 0xFF
            long_period_count = value & 0xFF
            return f"{value} (周期过短:{short_period_count}次, 周期过长:{long_period_count}次)"
        elif reg_addr == RegAddr.REG_COMPILER_TS_H or reg_addr == RegAddr.REG_COMPILER_TS_L:
            # 编译时间戳，需要组合高低位才有意义，这里只显示原值
            return f"{value} (编译时间戳{'高位' if reg_addr == RegAddr.REG_COMPILER_TS_H else '低位'})"
        elif reg_addr == RegAddr.REG_LOCATION_LATITUDE_H or reg_addr == RegAddr.REG_LOCATION_LATITUDE_L:
            # 纬度，需要组合高低位才有意义，这里只显示原值
            return f"{value} (纬度{'高位' if 'H' in RegAddr.get_reg_name(reg_addr) else '低位'})"
        elif reg_addr == RegAddr.REG_LOCATION_LONGITUDE_H or reg_addr == RegAddr.REG_LOCATION_LONGITUDE_L:
            # 经度，需要组合高低位才有意义，这里只显示原值
            return f"{value} (经度{'高位' if 'H' in RegAddr.get_reg_name(reg_addr) else '低位'})"
        else:
            # 其他寄存器直接显示整数值，不添加单位
            return str(value)
    except (ValueError, TypeError):
        return str(raw_value) if raw_value is not None else "--"


def update_device_version_and_type(device: Device, version_h_raw: int, version_l_raw: int) -> bool:
    """
    根据原始寄存器值更新设备版本号和设备类型

    Args:
        device: 设备对象
        version_h_raw: REG_VERSION_H原始值
        version_l_raw: REG_VERSION_L原始值

    Returns:
        bool: 更新是否成功
    """
    try:
        # 使用统一的版本解析工具
        from utils.ota_common import VersionUtils

        # 解析版本号
        new_version = VersionUtils.parse_version_from_registers(version_h_raw, version_l_raw)

        # 解析设备类型：REG_VERSION_H的高8位
        device_type = (version_h_raw >> 8) & 0xFF

        # 标记是否有更新
        has_update = False

        # 更新版本号（只有在版本号不同时才更新）
        if not device.firmware_version or new_version != device.firmware_version:
            device.firmware_version = new_version
            has_update = True
            logger.info(f"设备 {device.device_id} 版本号已更新为: {new_version}")
        else:
            logger.debug(f"设备 {device.device_id} 版本号未变化: {new_version}")

        # 更新设备类型
        if device_type in [10, 50, 51]:  # 检查设备类型是否有效
            # 只有当设备当前没有设置设备类型或设备类型不同时才更新
            if device.device_type != device_type:
                device.device_type = device_type
                has_update = True
                logger.info(f"设备 {device.device_id} 设备类型已更新为: {device_type}")
            else:
                logger.debug(f"设备 {device.device_id} 设备类型未变化: {device_type}")
        else:
            logger.warning(f"设备 {device.device_id} 设备类型无效: {device_type}")
            # 注意：设备类型无效不应该返回False，因为版本号可能已经成功更新

        # 如果有任何更新，提交到数据库
        if has_update:
            db.session.commit()

        return True

    except Exception as e:
        logger.error(f"更新设备版本号和类型异常: {e}")
        # 发生异常时回滚事务
        try:
            db.session.rollback()
        except Exception as rollback_error:
            logger.error(f"事务回滚失败: {rollback_error}")
        return False


logger = logging.getLogger(__name__)

class AsyncDeviceService:
    """异步设备服务类"""

    @staticmethod
    def query_device_parameters(device_id: str):
        """异步查询设备参数"""
        try:
            device = Device.query.get(device_id)
            if not device:
                raise Exception('设备不存在')

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                raise Exception('IoT客户端未启动，请先启动客户端')

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 构建topic
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

            # 创建寄存器管理器
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 读取所有寄存器参数
            raw_values = {}

            # 读取主要寄存器组
            register_groups = [
                (RegAddr.REG_VERSION_H, 20),  # 版本和基本信息
                (RegAddr.REG_T1, 13),         # 温度寄存器
                (RegAddr.REG_P4, 13),         # 功率寄存器
                (RegAddr.REG_UID_PROTECT_KEY2, 3),  # 新增的无线充电相关寄存器
            ]

            for start_addr, count in register_groups:
                try:
                    msg = reg_manager.read_registers(int(device.device_id), start_addr, count, timeout=15)
                    if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                        values = msg['parsed_data']['register_value']
                        for i, value in enumerate(values):
                            reg_addr = start_addr + i
                            reg_name = RegAddr.get_reg_name(reg_addr)
                            if reg_name:
                                raw_values[reg_name] = value
                except Exception as e:
                    logger.warning(f"读取寄存器组 {start_addr} 失败: {e}")
                    continue

            if not raw_values:
                raise Exception('无法读取设备参数，设备可能离线或无响应')

            # 保存原始值到数据库
            try:
                for reg_name, raw_value in raw_values.items():
                        reg_addr = RegAddr.get_reg_addr_by_name(reg_name)
                        device.set_register_value(reg_addr, raw_value)
            except Exception as e:
                logger.warning(e)

            # 更新设备版本号和类型
            if 'REG_VERSION_H' in raw_values and 'REG_VERSION_L' in raw_values:
                update_device_version_and_type(device, raw_values['REG_VERSION_H'], raw_values['REG_VERSION_L'])

            # 提交数据库更改
            from models.database import db
            db.session.commit()
            logger.info(f"设备 {device.device_id} 的参数已保存到数据库")

            # 构建返回结果（格式化显示）
            response = {}
            for reg_name, raw_value in raw_values.items():
                try:
                    reg_addr = RegAddr.get_reg_addr_by_name(reg_name)
                    formatted_value = format_register_value_for_display(reg_addr, raw_value)
                    response[reg_name] = {
                        'value': formatted_value,
                        'raw_value': raw_value,
                        'description': RegAddr.get_reg_desc(reg_name)
                    }
                except Exception as e:
                    logger.warning(f"格式化寄存器 {reg_name} 值失败: {e}")
                    response[reg_name] = {
                        'value': str(raw_value),
                        'raw_value': raw_value,
                        'description': ''
                    }

            return {
                'success': True,
                'parameters': response,
                'timestamp': datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"异步查询设备参数失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    @staticmethod
    def query_error_counts(device_id: str):
        """异步查询设备错误计数"""
        try:
            device = Device.query.get(device_id)
            if not device:
                raise Exception('设备不存在')

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                raise Exception('IoT客户端未启动，请先启动客户端')

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 构建topic
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

            # 创建寄存器管理器
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 一次性读取4个连续的寄存器
            msg = reg_manager.read_registers(int(device.device_id), RegAddr.REG_ERROR_CNT1, 4, timeout=15)
            if msg and 'parsed_data' in msg and 'register_value' in msg['parsed_data']:
                values = msg['parsed_data']['register_value']
                if len(values) == 4:
                    result = {
                        'REG_ERROR_CNT1': values[0],
                        'REG_ERROR_CNT2': values[1],
                        'REG_ERROR_CNT3': values[2],
                        'REG_ERROR_CNT4': values[3]
                    }
                    return {
                        'success': True,
                        'error_counts': result,
                        'timestamp': datetime.now().isoformat()
                    }

            # 如果读取失败，返回默认值
            return {
                'success': True,
                'error_counts': {
                    'REG_ERROR_CNT1': 0,
                    'REG_ERROR_CNT2': 0,
                    'REG_ERROR_CNT3': 0,
                    'REG_ERROR_CNT4': 0
                },
                'timestamp': datetime.now().isoformat(),
                'note': '读取失败，返回默认值'
            }

        except Exception as e:
            logger.error(f"异步查询错误计数失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    @staticmethod
    def query_debug_info(device_id: str):
        """异步查询设备调试信息"""
        try:
            device = Device.query.get(device_id)
            if not device:
                raise Exception('设备不存在')

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                raise Exception('IoT客户端未启动，请先启动客户端')

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 构建topic
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

            # 创建寄存器管理器
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 查询设备调试信息，返回响应消息
            msg = reg_manager.cmd_request_debug_info_query(int(device.device_id), 0, timeout=15)
            if msg:
                # 使用共享解析服务解析调试信息
                debug_response = DebugInfoParser.parse_debug_info(msg, device)
                
                # 转换为字典格式返回
                response_dict = DebugInfoParser.to_dict(debug_response)
                
                return {
                    'success': True,
                    'debug_info': {
                        'session_id': debug_response.session_id,
                        'result': debug_response.result,
                        'info': debug_response.info
                    },
                    'parsed_data': response_dict['parsed_data'],
                    'timestamp': debug_response.timestamp.isoformat()
                }
            else:
                raise Exception('获取设备调试信息失败')

        except Exception as e:
            logger.error(f"异步查询调试信息失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    @staticmethod
    def query_firmware_info(device_id: str):
        """异步查询设备固件信息"""
        try:
            device = Device.query.get(device_id)
            if not device:
                raise Exception('设备不存在')

            # 检查IoT客户端是否已启动
            if not IoTClientManager.is_running():
                raise Exception('IoT客户端未启动，请先启动客户端')

            # 使用全局IoT客户端
            iot_client = IoTClientManager.get_instance()

            # 构建topic
            topic_full_name = f"/{device.product_key}/{device.device_id}/user/ota"

            # 创建寄存器管理器
            reg_manager = RegisterManager(iot_client, topic_full_name, logger)

            # 查询设备固件信息，返回响应消息
            msg = reg_manager.cmd_request_firmware_info_query(int(device.device_id), timeout=15)
            if msg:
                # 检查是否有命令响应信息
                parsed_data = msg.get('parsed_data', {})
                # 提取需要的信息
                session_id = parsed_data.get('session_id', 0)
                result = parsed_data.get('result', 1)  # 默认为错误
                info = parsed_data.get('info', {})

                return {
                    'success': True,
                    'firmware_info': {
                        'session_id': session_id,
                        'result': result,
                        'info': info
                    },
                    'timestamp': datetime.now().isoformat()
                }
            else:
                raise Exception('获取设备固件信息失败')

        except Exception as e:
            logger.error(f"异步查询固件信息失败: {e}")
            return {
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
