<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>注册 - OTA设备管理系统</title>
    {% include 'base_css.html' %}
    <!-- Custom CSS -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    <style>
        body {
            margin: 0;
            padding: 0;
            background: url('https://source.unsplash.com/1920x1080/?technology,digital') no-repeat center center fixed;
            background-size: cover;
            position: relative;
            min-height: 100vh;
        }
        
        #particles-js {
            position: fixed;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 1;
            background: linear-gradient(45deg, #1a237e, #0d47a1);
            opacity: 0.8;
        }
        
        .register-container {
            position: relative;
            z-index: 2;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border: none;
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
            border-radius: 15px;
            overflow: hidden;
        }
        
        .card-header {
            background: rgba(13, 110, 253, 0.9) !important;
            border-bottom: none;
            padding: 1.5rem;
        }
        
        .btn-primary {
            background: linear-gradient(45deg, #2196F3, #4CAF50);
            border: none;
            transition: all 0.3s ease;
            padding: 12px;
            font-size: 1.1rem;
            font-weight: 500;
            letter-spacing: 1px;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.3);
            background: linear-gradient(45deg, #1976D2, #388E3C);
        }
        
        .input-group-text {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ced4da;
            border-right: none;
        }
        
        .form-control {
            background: rgba(255, 255, 255, 0.9);
            border: 1px solid #ced4da;
            border-left: none;
            padding: 12px;
            font-size: 1rem;
        }
        
        .form-control:focus {
            box-shadow: none;
            border-color: #80bdff;
            background: rgba(255, 255, 255, 0.95);
        }
        
        .input-group:focus-within .input-group-text {
            border-color: #80bdff;
        }
        
        .form-label {
            font-weight: 500;
            color: #495057;
            margin-bottom: 0.5rem;
        }
        
        .g-recaptcha {
            margin: 20px 0;
        }
        
        .back-to-login {
            color: #6c757d;
            text-decoration: none;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .back-to-login:hover {
            color: #4CAF50;
            transform: translateX(-5px);
        }

        .alert {
            border-radius: 10px;
            border: none;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .alert-success {
            background-color: rgba(76, 175, 80, 0.9);
            color: white;
        }

        .alert-danger {
            background-color: rgba(244, 67, 54, 0.9);
            color: white;
        }

        .captcha-container {
            background: rgba(255, 255, 255, 0.9);
            border-radius: 10px;
            padding: 10px;
            margin-top: 10px;
        }

        .captcha-text {
            font-family: 'Courier New', monospace;
            letter-spacing: 8px;
            font-size: 28px;
            font-weight: bold;
            color: #0d6efd;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
        }

        .password-requirements {
            font-size: 0.85rem;
            color: #6c757d;
            margin-top: 0.5rem;
        }

        .input-group {
            margin-bottom: 0.5rem;
        }

        .fade-in {
            animation: fadeIn 0.5s ease-in;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(-10px); }
            to { opacity: 1; transform: translateY(0); }
        }
    </style>
</head>
<body>
    <div id="particles-js"></div>
    <div class="container register-container">
        <div class="row justify-content-center align-items-center min-vh-100">
            <div class="col-md-6 col-lg-5">
                <div class="card shadow-lg border-0 rounded-lg fade-in">
                    <div class="card-header bg-primary text-white text-center py-4">
                        <h3 class="mb-0">
                            <i class="fas fa-user-plus me-2"></i>用户注册
                        </h3>
                    </div>
                    <div class="card-body p-4">
                        {% with messages = get_flashed_messages(with_categories=true) %}
                        {% if messages %}
                        {% for category, message in messages %}
                        <div class="alert alert-{{ category }} alert-dismissible fade show" role="alert">
                            {% if category == 'success' %}
                            <i class="fas fa-check-circle me-2"></i>
                            {% else %}
                            <i class="fas fa-exclamation-circle me-2"></i>
                            {% endif %}
                            {{ message }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        {% endfor %}
                        {% endif %}
                        {% endwith %}

                        <form method="POST" action="{{ url_for('auth.register') }}" class="needs-validation" novalidate>
                            {{ form.csrf_token }}
                            <div class="mb-3">
                                <label for="username" class="form-label">用户名</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-user"></i></span>
                                    <input type="text" class="form-control" id="username" name="username" required minlength="4" maxlength="25">
                                </div>
                                <div class="password-requirements">
                                    用户名长度需要在4-25个字符之间
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">邮箱</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-envelope"></i></span>
                                    <input type="email" class="form-control" id="email" name="email" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="password" class="form-label">密码</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="password" name="password" required minlength="6">
                                </div>
                                <div class="password-requirements">
                                    密码长度至少6个字符
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">确认密码</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-lock"></i></span>
                                    <input type="password" class="form-control" id="confirm_password" name="confirm_password" required>
                                </div>
                            </div>
                            
                            <div class="mb-4">
                                <label for="captcha" class="form-label">验证码</label>
                                <div class="input-group">
                                    <span class="input-group-text"><i class="fas fa-shield-alt"></i></span>
                                    <input type="text" class="form-control" id="captcha" name="captcha" required>
                                    <button type="button" class="btn btn-outline-secondary" onclick="refreshCaptcha()">
                                        <i class="fas fa-sync-alt"></i>
                                    </button>
                                </div>
                                <div class="captcha-container text-center mt-2">
                                    <span class="captcha-text"></span>
                                </div>
                            </div>
                            
                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary btn-lg">
                                    <i class="fas fa-user-plus me-2"></i>注册
                                </button>
                            </div>
                        </form>
                    </div>
                    <div class="card-footer text-center py-3">
                        <div class="small">
                            <a href="{{ url_for('auth.login') }}" class="back-to-login">
                                <i class="fas fa-arrow-left me-1"></i>返回登录
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/particles.js/2.0.0/particles.min.js"></script>

    <script>
        // 表单验证
        (function () {
            'use strict'
            var forms = document.querySelectorAll('.needs-validation')
            Array.prototype.slice.call(forms).forEach(function (form) {
                form.addEventListener('submit', function (event) {
                    if (!form.checkValidity()) {
                        event.preventDefault()
                        event.stopPropagation()
                    }
                    form.classList.add('was-validated')
                }, false)
            })
        })()

        // 刷新验证码
        function refreshCaptcha() {
            fetch('/captcha')
                .then(response => response.json())
                .then(data => {
                    document.querySelector('.captcha-text').textContent = data.captcha;
                });
        }

        // 页面加载时生成验证码
        document.addEventListener('DOMContentLoaded', function() {
            refreshCaptcha();
        });

        // 密码确认验证
        document.getElementById('confirm_password').addEventListener('input', function() {
            if (this.value !== document.getElementById('password').value) {
                this.setCustomValidity('密码不匹配');
            } else {
                this.setCustomValidity('');
            }
        });

        // 粒子效果配置
        particlesJS('particles-js', {
            particles: {
                number: {
                    value: 80,
                    density: {
                        enable: true,
                        value_area: 800
                    }
                },
                color: {
                    value: '#ffffff'
                },
                shape: {
                    type: 'circle'
                },
                opacity: {
                    value: 0.5,
                    random: false
                },
                size: {
                    value: 3,
                    random: true
                },
                line_linked: {
                    enable: true,
                    distance: 150,
                    color: '#ffffff',
                    opacity: 0.4,
                    width: 1
                },
                move: {
                    enable: true,
                    speed: 2,
                    direction: 'none',
                    random: false,
                    straight: false,
                    out_mode: 'out',
                    bounce: false
                }
            },
            interactivity: {
                detect_on: 'canvas',
                events: {
                    onhover: {
                        enable: true,
                        mode: 'repulse'
                    },
                    onclick: {
                        enable: true,
                        mode: 'push'
                    },
                    resize: true
                }
            },
            retina_detect: true
        });
    </script>
</body>
</html> 