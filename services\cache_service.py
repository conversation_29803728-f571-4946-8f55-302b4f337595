#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
缓存服务模块
提供统一的缓存管理功能，包括缓存键管理、失效策略等
"""

import logging
from typing import Any, Optional, List, Dict
from functools import wraps
from flask import current_app
from flask_caching import Cache
from utils.logger import LoggerManager

# 获取日志记录器
logger = LoggerManager.get_logger()

# 全局缓存实例
cache = Cache()


class CacheKeyManager:
    """缓存键管理器"""

    # 缓存键前缀
    DEVICE_LIST = "device_list"
    DEVICE_STATS = "device_stats"
    FIRMWARE_LIST = "firmware_list"
    OTA_TASK_LIST = "ota_task_list"
    OTA_TASK_STATS = "ota_task_stats"
    DEVICE_STATUS = "device_status"
    DEVICE_PARAMETERS = "device_parameters"

    @staticmethod
    def get_device_list_key(page: int = 1, per_page: int = 20, **filters) -> str:
        """获取设备列表缓存键"""
        filter_str = "_".join([f"{k}_{v}" for k, v in sorted(filters.items()) if v])
        return f"{CacheKeyManager.DEVICE_LIST}_{page}_{per_page}_{filter_str}"

    @staticmethod
    def get_ota_task_list_key(page: int = 1, per_page: int = 20, **filters) -> str:
        """获取OTA任务列表缓存键"""
        filter_str = "_".join([f"{k}_{v}" for k, v in sorted(filters.items()) if v])
        return f"{CacheKeyManager.OTA_TASK_LIST}_{page}_{per_page}_{filter_str}"

    @staticmethod
    def get_device_status_key(device_id: str) -> str:
        """获取设备状态缓存键"""
        return f"{CacheKeyManager.DEVICE_STATUS}_{device_id}"

    @staticmethod
    def get_device_parameters_key(device_id: str) -> str:
        """获取设备参数缓存键"""
        return f"{CacheKeyManager.DEVICE_PARAMETERS}_{device_id}"


class CacheService:
    """缓存服务类"""

    def __init__(self):
        self.cache = cache
        self._hit_count = 0
        self._miss_count = 0

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计信息"""
        total_requests = self._hit_count + self._miss_count
        hit_rate = (self._hit_count / total_requests * 100) if total_requests > 0 else 0

        return {
            "hit_count": self._hit_count,
            "miss_count": self._miss_count,
            "total_requests": total_requests,
            "hit_rate": round(hit_rate, 2),
        }

    def get(self, key: str) -> Any:
        """获取缓存数据"""
        try:
            data = self.cache.get(key)
            if data is not None:
                self._hit_count += 1
                logger.debug(f"缓存命中: {key}")
            else:
                self._miss_count += 1
                logger.debug(f"缓存未命中: {key}")
            return data
        except Exception as e:
            logger.error(f"获取缓存失败 {key}: {e}")
            self._miss_count += 1
            return None

    def set(self, key: str, value: Any, timeout: Optional[int] = None) -> bool:
        """设置缓存数据"""
        result = self.cache.set(key, value, timeout=timeout)
        if result:
            logger.debug(f"缓存设置成功: {key}")
        else:
            logger.warning(f"缓存设置失败: {key}")
        return result
    
    def delete(self, key: str) -> bool:
        """删除缓存数据"""
        result = self.cache.delete(key)
        logger.debug(f"缓存删除: {key}")
        return result
    
    def delete_many(self, keys: List[str]) -> bool:
        """批量删除缓存数据"""
        result = self.cache.delete_many(*keys)
        logger.debug(f"批量缓存删除: {len(keys)} 个键")
        return result    
    
    def clear_all(self) -> bool:
        """清除所有缓存"""
        result = self.cache.clear()
        logger.info("所有缓存已清除")
        return result    
    
    def invalidate_device_cache(self):
        """失效设备相关缓存"""
        patterns = [CacheKeyManager.DEVICE_LIST, CacheKeyManager.DEVICE_STATS, CacheKeyManager.DEVICE_STATUS, CacheKeyManager.DEVICE_PARAMETERS]

        # 由于SimpleCache不支持模式匹配，我们需要清除所有缓存
        # 在生产环境中使用Redis时可以实现更精确的模式匹配
        logger.info("失效设备相关缓存")
        return self.clear_all()

    def invalidate_device_parameters_cache(self, device_id: str):
        """失效指定设备的参数缓存"""
        cache_key = CacheKeyManager.get_device_parameters_key(device_id)
        result = self.delete(cache_key)
        logger.debug(f"设备 {device_id} 参数缓存已失效")
        return result

    def invalidate_ota_cache(self):
        """失效OTA任务相关缓存"""
        patterns = [CacheKeyManager.OTA_TASK_LIST, CacheKeyManager.OTA_TASK_STATS]

        logger.info("失效OTA任务相关缓存")
        return self.clear_all()

    def invalidate_firmware_cache(self):
        """失效固件相关缓存"""
        logger.info("失效固件相关缓存")
        return self.delete(CacheKeyManager.FIRMWARE_LIST)


# 全局缓存服务实例
cache_service = CacheService()


def cached_route(timeout: int = 300, key_func: Optional[callable] = None):
    """
    路由缓存装饰器

    Args:
        timeout: 缓存超时时间（秒）
        key_func: 自定义缓存键生成函数
    """

    def decorator(f):
        @wraps(f)
        def decorated_function(*args, **kwargs):
            # 生成缓存键
            if key_func:
                cache_key = key_func(*args, **kwargs)
            else:
                cache_key = f"{f.__name__}_{hash(str(args) + str(kwargs))}"

            # 尝试从缓存获取数据
            cached_data = cache_service.get(cache_key)
            if cached_data is not None:
                return cached_data

            # 执行原函数
            result = f(*args, **kwargs)

            # 缓存结果
            cache_service.set(cache_key, result, timeout=timeout)

            return result

        return decorated_function

    return decorator


def init_cache(app):
    """初始化缓存"""
    cache.init_app(app)
    logger.info(f"缓存初始化成功，类型: {app.config.get('CACHE_TYPE', 'Unknown')}")
    return True